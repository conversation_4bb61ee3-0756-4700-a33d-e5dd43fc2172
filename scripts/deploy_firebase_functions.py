import subprocess
import os
from dotenv import load_dotenv

# Load environment variables from .env file in functions directory
functions_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'functions')
env_path = os.path.join(functions_dir, '.env')
load_dotenv(env_path)

def get_env_vars():
    """Get all environment variables from .env file"""
    env_vars = []
    print("Checking environment variables...")
    for key, value in os.environ.items():
        if key in [
            'OPENAI_API_KEY',
            'OANDA_API_KEY',
            'OANDA_API_URL',
            'BUCKET_NAME',
            'CSV_FILENAME',
            'DEFAULT_SYMBOL',
            'DEFAULT_TIMEFRAME',
            'STRIPE_SECRET_KEY',
            'STRIPE_PUBLISHABLE_KEY',
            'USE_FIREBASE_EMULATOR',
            'LOCAL_FOREX_DATA_DIR'
        ]:
            print(f"Found {key}")
            env_vars.append(f"{key}={value}")

    result = ','.join(env_vars)
    print(f"Environment variables string: {result}")
    return result

def deploy_http_function(function_name, memory="256Mi"):
    """
    Deploys an HTTP-triggered Firebase Cloud Function.
    """
    command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        f"--memory={memory}",
        "--trigger-http",
        "--no-allow-unauthenticated",
        "--source=functions/",
        f"--set-env-vars={get_env_vars()}",
        function_name,
        f"--entry-point={function_name}"
    ]

    try:
        print(f"Deploying HTTP function: {function_name}")
        subprocess.run(command, check=True)
        print(f"Function {function_name} deployed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error deploying function {function_name}: {e}")
    except FileNotFoundError:
        print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def deploy_firestore_function(function_name):
    """
    Deploys a Firestore-triggered Firebase Cloud Function.
    """
    command = [
        "gcloud", "functions", "deploy",
        "--gen2",
        "--region=us-central1",
        "--project=oryntrade",
        "--runtime=python312",
        "--build-service-account=projects/oryntrade/serviceAccounts/<EMAIL>",
        "--memory=1024Mi",
        "--trigger-event-filters=type=google.cloud.firestore.document.v1.created",
        "--trigger-event-filters=database=(default)",
        "--trigger-event-filters-path-pattern=document=users/{userId}/submittedStrategies/{strategyId}",
        "--trigger-location=nam5",
        "--source=functions/",
        "--no-allow-unauthenticated",
        f"--set-env-vars={get_env_vars()},FUNCTION_SIGNATURE_TYPE=cloudevent,FUNCTION_TARGET=publish_strategy",
        function_name,
        f"--entry-point={function_name}"
    ]

    try:
        print(f"Deploying Firestore function: {function_name}")
        subprocess.run(command, check=True)
        print(f"Function {function_name} deployed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error deploying function {function_name}: {e}")
    except FileNotFoundError:
        print("Error: gcloud command not found. Make sure Google Cloud SDK is installed.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

# List of HTTP functions to deploy
http_functions = [
    "signup",
    "get_user",
    "register_api_key",
    "change_password",
    "delete_account",
    "get_oanda_account",
    "save_strategy",
    "update_strategy",
    "get_strategies"
]

# List of Firestore-triggered functions
firestore_functions = [
    "publish_strategy"
]

# List of functions that need more memory
high_memory_functions = {
    "run_backtest": "1024Mi",  # 1GB for complex calculations
    "ai_strategy": "512Mi"     # 512MB for OpenAI API calls
}

if __name__ == "__main__":
    # Ensure we're in the project root directory
    os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Deploy HTTP functions
    for function_name in http_functions:
        deploy_http_function(function_name)

    # Deploy high-memory HTTP functions
    for function_name, memory in high_memory_functions.items():
        deploy_http_function(function_name, memory)

    # Deploy Firestore-triggered functions
    for function_name in firestore_functions:
        deploy_firestore_function(function_name)