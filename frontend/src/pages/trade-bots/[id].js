import { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../../firebaseConfig";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import MetricCard from "../../components/MetricCard";
import TradeHistoryRow from "../../components/TradeHistoryRow";
import StrategyRules from "../../components/StrategyRules";
import StrategyVisualization from "../../components/StrategyVisualization";
import RiskManagementPanel from "../../components/RiskManagementPanel";
import StatusNotification from "../../components/StatusNotification";
import UserLogs from "../../components/UserLogs";
import PerformanceMetrics from "../../components/PerformanceMetrics";
import TradeNotification from "../../components/TradeNotification";
import MarketConditionsPanel from "../../components/MarketConditionsPanel";
import {
  getDoc,
  doc,
  getDocs,
  query,
  where,
  collection,
  updateDoc,
} from "firebase/firestore";
import { db } from "../../../firebaseConfig";
import OptimizedTradingChart from "../../components/OptimizedTradingChart";
import { USE_FIREBASE_EMULATOR } from "../../config";

const DashboardLayout = dynamic(
  () => import("../../components/DashboardLayout"),
  {
    ssr: false,
  }
);

// Define control strategy URL
const CONTROL_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://localhost:8080"
  : "https://control-strategy-ihjc6tjxia-uc.a.run.app";

export default function TradeBotDetail() {
  const router = useRouter();
  const { id } = router.query;
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [botStatus, setBotStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [tradeLogs, setTradeLogs] = useState([]);
  const [strategy, setStrategy] = useState({});
  const [openTrades, setOpenTrades] = useState([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [candleData, setCandleData] = useState([]);
  const [indicators, setIndicators] = useState({});
  const [historicalTrades, setHistoricalTrades] = useState([]);
  const [botPerformance, setBotPerformance] = useState({
    totalPnL: 0,
    winRate: 0,
    totalTrades: 0,
  });
  const [userTimezone, setUserTimezone] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );
  const [lastHeartbeat, setLastHeartbeat] = useState(null);
  const [marketStatus, setMarketStatus] = useState(null);
  const [accountBalance, setAccountBalance] = useState(null);
  const [strategySummary, setStrategySummary] = useState({
    totalTrades: 0,
    totalRealizedPL: 0,
    totalUnrealizedPL: 0,
    winRate: 0,
    maxDrawdown: 0,
    sharpeRatio: 0,
    profitFactor: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
  });
  const [riskManagement, setRiskManagement] = useState(null);
  const [isColumnMenuOpen, setIsColumnMenuOpen] = useState(false);
  const columnButtonRef = useRef(null);
  const columnMenuRef = useRef(null);

  // Handle click outside to close the column menu
  useEffect(() => {
    function handleClickOutside(event) {
      if (columnMenuRef.current && !columnMenuRef.current.contains(event.target) &&
          columnButtonRef.current && !columnButtonRef.current.contains(event.target)) {
        setIsColumnMenuOpen(false);
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // New state variables for enhanced user experience
  const [previousStatus, setPreviousStatus] = useState(null);
  const [showStatusNotification, setShowStatusNotification] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [lastTradeTimestamp, setLastTradeTimestamp] = useState(null);
  const notificationsRef = useRef([]);

  // Handle authentication
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
      }
    });
    return () => unsubscribe();
  }, [router]);

  // Update running clock every second
  useEffect(() => {
    const clockInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(clockInterval);
  }, []);

  // State for controlling auto-refresh
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(60000); // 60 seconds default

  // Check if current time is in trading session
  const isInTradingSession = () => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    const currentHour = new Date().getUTCHours();

    // Check if current time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return currentHour >= 13 && currentHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return currentHour >= 8 && currentHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return currentHour >= 0 && currentHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return currentHour >= 22 || currentHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  // Poll bot status and data at the specified interval
  useEffect(() => {
    if (firebaseUser?.uid && id) {
      fetchBotData();

      let interval;
      if (autoRefresh) {
        console.log(`Setting up auto-refresh every ${refreshInterval/1000} seconds`);
        interval = setInterval(fetchBotData, refreshInterval);
      }

      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [firebaseUser?.uid, id, autoRefresh, refreshInterval]);

  const calculatePerformance = (trades) => {
    if (!trades || trades.length === 0) {
      return { totalPnL: 0, winRate: 0, totalTrades: 0 };
    }

    const closedTrades = trades.filter(
      (trade) => trade.status?.toUpperCase() === "CLOSED"
    );
    const winningTrades = closedTrades.filter(
      (trade) => parseFloat(trade.realizedPL) > 0
    );

    // Calculate total P/L: sum of realized P/L from all closed trades + unrealized P/L from open trades
    const totalPnL = trades.reduce((sum, trade) => {
      if (trade.status?.toUpperCase() === "CLOSED") {
        return sum + (parseFloat(trade.realizedPL) || 0);
      } else {
        return sum + (parseFloat(trade.unrealizedPL) || 0);
      }
    }, 0);

    return {
      totalPnL,
      winRate:
        closedTrades.length > 0
          ? winningTrades.length / closedTrades.length
          : 0,
      totalTrades: trades.length,
    };
  };

  const fetchBotData = async () => {
    try {
      setLoading(true);

      const botDoc = await getDoc(
        doc(db, "users", firebaseUser.uid, "submittedStrategies", id)
      );

      if (!botDoc.exists()) {
        console.error("Bot not found");
        router.push("/trade-bots");
        return;
      }

      const strategyData = botDoc.data();
      console.log("Strategy data from Firestore:", strategyData);
      console.log("Human readable rules:", strategyData.human_readable_rules);
      console.log("Strategy info:", strategyData.human_readable_rules?.strategy_info);
      console.log("Strategy timeframe:", strategyData.human_readable_rules?.strategy_info?.timeframe);

      // Parse strategy_json if it's a string
      if (strategyData.strategy_json && typeof strategyData.strategy_json === 'string') {
        try {
          strategyData.strategy_json = JSON.parse(strategyData.strategy_json);
          console.log("Parsed strategy_json:", strategyData.strategy_json);

          // Check if risk management data exists in the strategy JSON
          if (strategyData.strategy_json.riskManagement) {
            console.log("Found risk management in strategy_json:", strategyData.strategy_json.riskManagement);
          } else if (strategyData.strategy_json.risk_management) {
            console.log("Found risk_management in strategy_json:", strategyData.strategy_json.risk_management);
            // Convert risk_management to riskManagement for consistency
            strategyData.strategy_json.riskManagement = strategyData.strategy_json.risk_management;
            delete strategyData.strategy_json.risk_management;
          }
        } catch (e) {
          console.error("Error parsing strategy_json:", e);
        }
      }

      setStrategy(strategyData);

      // Get bot status
      const status = strategyData.status || "unknown";

      // Check if status has changed
      if (botStatus !== status && botStatus !== null) {
        setPreviousStatus(botStatus);
        setShowStatusNotification(true);

        // Add status change to notifications
        const statusNotification = {
          id: Date.now(),
          type: 'status',
          message: `Bot status changed from ${botStatus.toUpperCase()} to ${status.toUpperCase()}`,
          timestamp: new Date().toISOString(),
          details: { previousStatus: botStatus, newStatus: status }
        };

        const updatedNotifications = [statusNotification, ...notificationsRef.current].slice(0, 10);
        setNotifications(updatedNotifications);
        notificationsRef.current = updatedNotifications;
      }

      // Check if we're in a trading session
      const inTradingSession = isInTradingSession();

      // If we have trading sessions defined and we're not in a session, override the status
      const tradingSessions = strategyData.strategy_json?.tradingSession || [];
      if (tradingSessions.length > 0 && !inTradingSession && status === "running") {
        // Only override if the bot is running
        setBotStatus("not_in_session");
        setIsRunning(false);
      } else {
        // Normal status handling
        setBotStatus(status);
        setIsRunning(status === "running");
      }

      setLastHeartbeat(strategyData.last_heartbeat);
      setTradeLogs(strategyData.user_logs || []);
      setMarketStatus(strategyData.marketStatus);

      const accountBalanceData = strategyData.accountBalance;
      if (
        accountBalanceData &&
        typeof accountBalanceData.balance === "number"
      ) {
        setAccountBalance(accountBalanceData.balance);
      } else {
        setAccountBalance(0);
      }

      // Get risk management data
      const riskManagementData = strategyData.riskManagement;
      console.log("Raw strategy data:", strategyData);
      console.log("Risk management data available:", !!riskManagementData);
      if (riskManagementData) {
        console.log("Risk management data:", riskManagementData);
        console.log("Risk management parameters:", riskManagementData.parameters);
        console.log("Risk management metrics:", riskManagementData.metrics);

        // Ensure metrics are properly initialized
        if (!riskManagementData.metrics) {
          riskManagementData.metrics = {
            dailyLoss: 0,
            totalProfit: 0,
            totalLoss: 0,
            runtimeProgress: 0,
            elapsedDays: 0,
            daysRemaining: riskManagementData.parameters?.runtime || 7,
            accountBalance: accountBalance,
            lastUpdated: new Date().toISOString()
          };
        }

        // Ensure metrics are up-to-date with the latest trade data
        if (historicalTrades && historicalTrades.length > 0) {
          let calculatedTotalProfit = 0;
          let calculatedTotalLoss = 0;

          // Calculate total profit and loss from trade history
          historicalTrades.forEach(trade => {
            if (trade.status === 'CLOSED' && trade.realizedPL) {
              const realizedPL = parseFloat(trade.realizedPL);
              if (realizedPL > 0) {
                calculatedTotalProfit += realizedPL;
              } else if (realizedPL < 0) {
                calculatedTotalLoss += Math.abs(realizedPL);
              }
            }
          });

          console.log("Calculated from trade history - Total Profit:", calculatedTotalProfit, "Total Loss:", calculatedTotalLoss);
          console.log("Current metrics - Total Profit:", riskManagementData.metrics.totalProfit, "Total Loss:", riskManagementData.metrics.totalLoss);

          // Always update metrics with calculated values from trade history
          // This ensures we have the most up-to-date values
          console.log("Updating metrics with calculated values from trade history");
          riskManagementData.metrics.totalProfit = calculatedTotalProfit;
          riskManagementData.metrics.totalLoss = calculatedTotalLoss;

          // Log the updated metrics
          console.log("Updated metrics - Total Profit:", riskManagementData.metrics.totalProfit,
                     "Total Loss:", riskManagementData.metrics.totalLoss);
        }

        // Calculate absolute values if they're missing
        if (riskManagementData.metrics && !riskManagementData.metrics.totalProfitTargetAbs) {
          const params = riskManagementData.parameters || {};
          const metrics = riskManagementData.metrics;
          const balance = metrics.accountBalance || accountBalance;

          // Parse percentage values
          const parsePercentage = (value) => {
            if (!value) return 0;
            if (typeof value === 'string') {
              return parseFloat(value.replace('%', ''));
            }
            return value;
          };

          // Calculate absolute values
          const totalProfitTarget = parsePercentage(params.totalProfitTarget);
          const totalLossLimit = parsePercentage(params.totalLossLimit);
          const maxDailyLoss = parsePercentage(params.maxDailyLoss);

          metrics.totalProfitTargetAbs = balance * (totalProfitTarget / 100);
          metrics.totalLossLimitAbs = balance * (totalLossLimit / 100);
          metrics.dailyLossLimitAbs = balance * (maxDailyLoss / 100);

          console.log("Calculated absolute values:", {
            totalProfitTargetAbs: metrics.totalProfitTargetAbs,
            totalLossLimitAbs: metrics.totalLossLimitAbs,
            dailyLossLimitAbs: metrics.dailyLossLimitAbs
          });
        }

        setRiskManagement(riskManagementData);
      } else {
        // Create a default risk management object for testing
        // Remove this in production
        const defaultRiskManagement = {
          parameters: {
            riskPerTrade: "1%",
            maxDailyLoss: "5%",
            stopLoss: "2%",
            takeProfit: "4%",
            maxPositionSize: "10%",
            totalProfitTarget: "20%",
            totalLossLimit: "10%",
            runtime: 7
          },
          metrics: {
            dailyLoss: 0,
            totalProfit: 0,
            totalLoss: 0,
            runtimeProgress: 0,
            elapsedDays: 0,
            daysRemaining: 7,
            accountBalance: accountBalance,
            totalProfitTargetAbs: accountBalance * 0.2,
            totalLossLimitAbs: accountBalance * 0.1,
            dailyLossLimitAbs: accountBalance * 0.05,
            lastUpdated: new Date().toISOString()
          }
        };
        console.log("Using default risk management data for testing:", defaultRiskManagement);
        setRiskManagement(defaultRiskManagement);
      }

      const summary = strategyData.summary || {
        totalTrades: 0,
        totalRealizedPL: 0,
        totalUnrealizedPL: 0,
        winRate: 0,
        maxDrawdown: 0,
        sharpeRatio: 0,
        profitFactor: 0,
        averageWin: 0,
        averageLoss: 0,
        largestWin: 0,
        largestLoss: 0,
      };
      setStrategySummary(summary);

      // Always fetch trade history regardless of bot status
      const tradeHistoryRef = collection(
        db,
        "users",
        firebaseUser.uid,
        "submittedStrategies",
        id,
        "tradeHistory"
      );
      const tradeHistorySnapshot = await getDocs(tradeHistoryRef);
      const trades = tradeHistorySnapshot.docs.map((doc) => {
        const data = doc.data();
        console.log("Trade data from Firestore:", doc.id, data);
        console.log("closeTime type:", typeof data.closeTime, data.closeTime);
        return {
          id: doc.id,
          ...data,
          openTime:
            data.openTime?.toDate?.() ||
            new Date(
              data.openTime?.seconds * 1000 +
                (data.openTime?.nanoseconds || 0) / 1000000
            ),
          closeTime:
            data.closeTime?.toDate?.() ||
            (data.closeTime
              ? typeof data.closeTime === 'string'
                ? new Date(data.closeTime)
                : new Date(
                    data.closeTime.seconds * 1000 +
                      (data.closeTime.nanoseconds || 0) / 1000000
                  )
              : null),
          price: parseFloat(data.price),
          units: parseFloat(data.units),
          initialMarginRequired: parseFloat(data.initialMarginRequired),
          halfSpreadCost: parseFloat(data.halfSpreadCost || 0),
          commission: parseFloat(data.commission || 0),
          realizedPL: parseFloat(data.realizedPL || 0),
          unrealizedPL: parseFloat(data.unrealizedPL || 0),
          takeProfitPrice: data.takeProfitPrice ? parseFloat(data.takeProfitPrice) : null,
          stopLossPrice: data.stopLossPrice ? parseFloat(data.stopLossPrice) : null,
        };
      });

      // Set both open trades and historical trades
      const openTradesFiltered = trades.filter(trade =>
        trade.status === 'OPEN' || trade.status === 'open' ||
        (trade.status !== 'CLOSED' && trade.status !== 'closed' && !trade.closeTime)
      );

      // Add take profit and stop loss prices from risk management if available
      if (riskManagement && riskManagement.parameters) {
        const { stopLoss, takeProfit } = riskManagement.parameters;

        // Process each open trade to add TP and SL if not already present
        const processedOpenTrades = openTradesFiltered.map(trade => {
          if (!trade.takeProfitPrice && takeProfit && trade.price) {
            const tpPercent = parseFloat(takeProfit) / 100;
            // Calculate TP based on direction (long/short)
            if (trade.type === 'long' || trade.units > 0) {
              trade.takeProfitPrice = trade.price * (1 + tpPercent);
            } else {
              trade.takeProfitPrice = trade.price * (1 - tpPercent);
            }
          }

          if (!trade.stopLossPrice && stopLoss && trade.price) {
            const slPercent = parseFloat(stopLoss) / 100;
            // Calculate SL based on direction (long/short)
            if (trade.type === 'long' || trade.units > 0) {
              trade.stopLossPrice = trade.price * (1 - slPercent);
            } else {
              trade.stopLossPrice = trade.price * (1 + slPercent);
            }
          }

          return trade;
        });

        console.log("Open trades with TP/SL added:", processedOpenTrades);
        setOpenTrades(processedOpenTrades);
      } else {
        console.log("Open trades filtered (no risk management data):", openTradesFiltered);
        setOpenTrades(openTradesFiltered);
      }

      setHistoricalTrades(trades);

      console.log("Fetched trade history:", trades);
      console.log("Trades with realized losses:", trades.filter(trade => trade.status === 'CLOSED' && parseFloat(trade.realizedPL) < 0));

      // Calculate performance metrics from trade history
      const closedTrades = trades.filter(trade =>
        trade.status === 'CLOSED' ||
        (trade.closeTime && trade.realizedPL !== undefined)
      );

      if (closedTrades.length > 0) {
        // Calculate total realized P&L
        const totalRealizedPL = closedTrades.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0);

        // Calculate winning and losing trades
        const winningTrades = closedTrades.filter(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
          return realizedPL > 0;
        }).length;

        const losingTrades = closedTrades.filter(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
          return realizedPL <= 0;
        }).length;

        // Calculate win rate
        const winRate = closedTrades.length > 0 ? winningTrades / closedTrades.length : 0;

        // Update strategy summary with calculated metrics
        const updatedSummary = {
          ...summary,
          totalTrades: closedTrades.length,
          totalRealizedPL: totalRealizedPL,
          winRate: winRate,
          winningTrades: winningTrades,
          losingTrades: losingTrades
        };

        console.log("Updated strategy summary with calculated metrics:", updatedSummary);
        setStrategySummary(updatedSummary);
      }

      // Check for new trades since last fetch
      if (lastTradeTimestamp) {
        const newTrades = trades.filter(trade => {
          const tradeTime = trade.openTime instanceof Date ? trade.openTime : new Date(trade.openTime);
          return tradeTime > new Date(lastTradeTimestamp);
        });

        // Create notifications for new trades
        if (newTrades.length > 0) {
          const tradeNotifications = newTrades.map(trade => {
            const isOpen = trade.status === 'OPEN';
            const hasProfitLoss = trade.realizedPL !== undefined && trade.realizedPL !== null;
            const isProfitable = hasProfitLoss && parseFloat(trade.realizedPL) > 0;

            let notificationType = isOpen ? 'trade_executed' : 'trade_closed';
            if (!isOpen && hasProfitLoss) {
              notificationType = isProfitable ? 'profit' : 'loss';
            }

            return {
              id: Date.now() + Math.random(),
              type: notificationType,
              message: isOpen
                ? `New ${trade.type.toUpperCase()} trade opened for ${trade.instrument}`
                : `${trade.type.toUpperCase()} trade closed for ${trade.instrument} with ${isProfitable ? 'profit' : 'loss'}`,
              timestamp: new Date().toISOString(),
              details: {
                tradeID: trade.id,
                instrument: trade.instrument,
                type: trade.type.toUpperCase(),
                units: trade.units,
                price: trade.price,
                realizedPL: trade.realizedPL
              }
            };
          });

          const updatedNotifications = [...tradeNotifications, ...notificationsRef.current].slice(0, 10);
          setNotifications(updatedNotifications);
          notificationsRef.current = updatedNotifications;
        }
      }

      // Update last trade timestamp
      setLastTradeTimestamp(new Date().toISOString());

      const performance = calculatePerformance(trades);
      setBotPerformance(performance);

      // Only fetch and set chart data if bot is running
      if (strategyData.status === "running") {
        const chartData = strategyData.chartData || {};
        console.log("Raw chart data from Firestore:", chartData);

        const candles = (chartData.candles || []).map((candle) => ({
          time: candle.time,
          open: parseFloat(candle.open),
          high: parseFloat(candle.high),
          low: parseFloat(candle.low),
          close: parseFloat(candle.close),
          volume: parseFloat(candle.volume || 0),
        }));
        console.log("Formatted candles:", candles[0]);

        // Debug indicators structure
        console.log("Raw indicators from Firestore:", chartData.indicators);
        if (chartData.indicators) {
          console.log("Indicator keys:", Object.keys(chartData.indicators));
          console.log("RSI indicator check:",
            Object.values(chartData.indicators).find(ind =>
              ind.type === 'RSI' || ind.indicator_class === 'RSI'
            )
          );
        }

        // Process indicators with simplified logic
        console.log("Indicator structure:", chartData.indicators);

        // Get RSI data with fallbacks for backward compatibility
        let rsiData = [];

        // First, try to get RSI data directly by type (new format)
        if (chartData.indicators?.RSI) {
          console.log("Found RSI data by type key (new format)");
          rsiData = chartData.indicators.RSI;
        }
        // Fallback to lowercase 'rsi' (alternative format)
        else if (chartData.indicators?.rsi) {
          console.log("Found RSI data by lowercase type key");
          rsiData = chartData.indicators.rsi;
        }
        // Last resort: look for RSI in the strategy JSON indicators
        else if (strategy?.strategy_json?.indicators) {
          console.log("Looking for RSI in strategy JSON indicators");
          const rsiIndicator = strategy.strategy_json.indicators.find(ind =>
            ind.type === 'RSI' || ind.indicator_class === 'RSI'
          );

          if (rsiIndicator && rsiIndicator.id && chartData.indicators[rsiIndicator.id]) {
            console.log("Found RSI by ID from strategy JSON:", rsiIndicator.id);
            rsiData = chartData.indicators[rsiIndicator.id];
          }
        }

        // Ensure RSI data is properly formatted
        if (rsiData.length > 0) {
          console.log("RSI data found, sample:", rsiData.slice(0, 3));

          // Format the data if it's not already in the right format
          if (typeof rsiData[0] !== 'object' || !('time' in rsiData[0])) {
            console.log("Formatting RSI data with timestamps");
            rsiData = rsiData.map((value, index) => ({
              time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
              value: typeof value === 'number' ? value :
                     typeof value === 'object' && value !== null ? (value.value || 0) : 0
            }));
          }
        } else {
          console.log("No RSI data found");
        }

        console.log("RSI data sample:", rsiData.slice(0, 3));

        // Process SMA data with the same approach as RSI
        let smaData = [];

        // First, try to get SMA data directly by type (new format)
        if (chartData.indicators?.SMA) {
          console.log("Found SMA data by type key (new format)");
          smaData = chartData.indicators.SMA;
        }
        // Fallback to lowercase 'sma' (alternative format)
        else if (chartData.indicators?.sma) {
          console.log("Found SMA data by lowercase type key");
          smaData = chartData.indicators.sma;
        }
        // Last resort: look for SMA in the strategy JSON indicators
        else if (strategy?.strategy_json?.indicators) {
          console.log("Looking for SMA in strategy JSON indicators");
          const smaIndicator = strategy.strategy_json.indicators.find(ind =>
            ind.type === 'SMA' || ind.indicator_class === 'SMA'
          );

          if (smaIndicator && smaIndicator.id && chartData.indicators[smaIndicator.id]) {
            console.log("Found SMA by ID from strategy JSON:", smaIndicator.id);
            smaData = chartData.indicators[smaIndicator.id];
          }
        }

        // Ensure SMA data is properly formatted
        if (smaData.length > 0) {
          console.log("SMA data found, sample:", smaData.slice(0, 3));

          // Format the data if it's not already in the right format
          if (typeof smaData[0] !== 'object' || !('time' in smaData[0])) {
            console.log("Formatting SMA data with timestamps");
            smaData = smaData.map((value, index) => ({
              time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
              value: typeof value === 'number' ? value :
                     typeof value === 'object' && value !== null ? (value.value || 0) : 0
            }));
          }
        } else {
          console.log("No SMA data found");
        }

        console.log("SMA data sample:", smaData.slice(0, 3));

        // Process EMA data with the same approach as SMA
        let emaData = [];
        let emaDataByIndicatorId = {};

        // First, try to get EMA data directly by type (new format)
        if (chartData.indicators?.EMA) {
          console.log("Found EMA data by type key (new format)");
          emaData = chartData.indicators.EMA;
        }
        // Fallback to lowercase 'ema' (alternative format)
        else if (chartData.indicators?.ema) {
          console.log("Found EMA data by lowercase type key");
          emaData = chartData.indicators.ema;
        }
        // Last resort: look for EMA in the strategy JSON indicators
        else if (strategy?.strategy_json?.indicators) {
          console.log("Looking for EMA in strategy JSON indicators");
          const emaIndicator = strategy.strategy_json.indicators.find(ind =>
            ind.type === 'EMA' || ind.indicator_class === 'EMA'
          );

          if (emaIndicator && emaIndicator.id && chartData.indicators[emaIndicator.id]) {
            console.log("Found EMA by ID from strategy JSON:", emaIndicator.id);
            emaData = chartData.indicators[emaIndicator.id];
          }
        }

        // Ensure EMA data is properly formatted
        if (emaData.length > 0) {
          console.log("EMA data found, sample:", emaData.slice(0, 3));

          // Format the data if it's not already in the right format
          if (typeof emaData[0] !== 'object' || !('time' in emaData[0])) {
            console.log("Formatting EMA data with timestamps");
            emaData = emaData.map((value, index) => ({
              time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
              value: typeof value === 'number' ? value :
                     typeof value === 'object' && value !== null ? (value.value || 0) : 0
            }));
          }
        } else {
          console.log("No EMA data found");
        }

        console.log("EMA data sample:", emaData.slice(0, 3));

        // Process all indicators from the strategy JSON
        // This will collect all indicators by their type and ID
        const indicatorsByType = {};
        const indicatorsByTypeAndId = {};

        // Initialize with empty arrays for common indicator types
        const commonIndicatorTypes = ['SMA', 'EMA', 'RSI', 'MACD', 'BollingerBands', 'ATR', 'ADX', 'CCI', 'Stochastic', 'Momentum'];
        commonIndicatorTypes.forEach(type => {
          indicatorsByType[type] = [];
          indicatorsByType[type.toLowerCase()] = [];
          indicatorsByTypeAndId[type] = {};
          indicatorsByTypeAndId[type.toLowerCase()] = {};
        });

        // First, try to get indicator data directly by type (new format)
        Object.entries(chartData.indicators || {}).forEach(([key, data]) => {
          // Check if the key is a known indicator type
          const upperKey = key.toUpperCase();
          if (commonIndicatorTypes.includes(upperKey)) {
            console.log(`Found ${upperKey} data by type key`);
            indicatorsByType[upperKey] = data;
            indicatorsByType[key] = data; // Also store with original case
          }
        });

        // Look for all indicators in the strategy JSON
        if (strategy?.strategy_json?.indicators) {
          console.log("Processing all indicators from strategy JSON");

          strategy.strategy_json.indicators.forEach(indicator => {
            const indicatorType = indicator.type || indicator.indicator_class;
            if (!indicatorType) return;

            const indicatorId = indicator.id;
            if (!indicatorId || !chartData.indicators[indicatorId]) return;

            console.log(`Found ${indicatorType} indicator with ID ${indicatorId}`);

            let indicatorData = chartData.indicators[indicatorId];

            // Format the data if it's not already in the right format
            if (indicatorData.length > 0) {
              if (typeof indicatorData[0] !== 'object' || !('time' in indicatorData[0])) {
                indicatorData = indicatorData.map((value, index) => ({
                  time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
                  value: typeof value === 'number' ? value :
                         typeof value === 'object' && value !== null ? (value.value || 0) : 0
                }));
              }

              // Store the formatted data with the indicator type and ID
              if (!indicatorsByTypeAndId[indicatorType]) {
                indicatorsByTypeAndId[indicatorType] = {};
              }

              indicatorsByTypeAndId[indicatorType][indicatorId] = {
                data: indicatorData,
                parameters: indicator.parameters,
                id: indicatorId,
                type: indicatorType
              };

              // Also store with lowercase type for case-insensitive access
              const lowerType = indicatorType.toLowerCase();
              if (!indicatorsByTypeAndId[lowerType]) {
                indicatorsByTypeAndId[lowerType] = {};
              }

              indicatorsByTypeAndId[lowerType][indicatorId] = {
                data: indicatorData,
                parameters: indicator.parameters,
                id: indicatorId,
                type: indicatorType
              };

              // If we don't have any data for this type yet, use this as the default
              if (!indicatorsByType[indicatorType] || indicatorsByType[indicatorType].length === 0) {
                indicatorsByType[indicatorType] = indicatorData;
              }

              // Also store with lowercase type for case-insensitive access
              if (!indicatorsByType[lowerType] || indicatorsByType[lowerType].length === 0) {
                indicatorsByType[lowerType] = indicatorData;
              }
            }
          });
        }

        // For backward compatibility, update existing variables with data from the new structure
        // Only update if we don't already have data
        if (emaData.length === 0) {
          emaData = indicatorsByType['EMA'] || indicatorsByType['ema'] || [];
        }
        emaDataByIndicatorId = indicatorsByTypeAndId['EMA'] || indicatorsByTypeAndId['ema'] || {};

        if (smaData.length === 0) {
          smaData = indicatorsByType['SMA'] || indicatorsByType['sma'] || [];
        }

        if (rsiData.length === 0) {
          rsiData = indicatorsByType['RSI'] || indicatorsByType['rsi'] || [];
        }

        // Format data if needed
        if (emaData.length > 0 && Object.keys(emaDataByIndicatorId).length === 0) {
          console.log("Only generic EMA data found, sample:", emaData.slice(0, 3));

          // Format the data if it's not already in the right format
          if (typeof emaData[0] !== 'object' || !('time' in emaData[0])) {
            console.log("Formatting EMA data with timestamps");
            emaData = emaData.map((value, index) => ({
              time: chartData.candles[index]?.time || Math.floor(Date.now() / 1000) - (index * 60),
              value: typeof value === 'number' ? value :
                     typeof value === 'object' && value !== null ? (value.value || 0) : 0
            }));
          }
        }

        // Log indicator counts
        Object.entries(indicatorsByTypeAndId).forEach(([type, indicators]) => {
          const count = Object.keys(indicators).length;
          if (count > 0) {
            console.log(`Found ${count} ${type} indicators`);
            // Debug: Log details of each indicator
            Object.entries(indicators).forEach(([id, info]) => {
              console.log(`  - ${type} ${id}: period=${info.parameters?.period}, data length=${info.data?.length}`);
            });
          }
        });

        console.log("EMA data sample:", emaData.slice(0, 3));

        // Create the indicators object with all indicator types
        const indicators = {
          // Add standard indicator data for backward compatibility
          rsi: rsiData.map((point) => ({
            time: point.time,
            value: parseFloat(point.value || 0),
          })),
          sma: smaData.map((point) => ({
            time: point.time,
            value: parseFloat(point.value || 0),
          })),
          ema: emaData.map((point) => ({
            time: point.time,
            value: parseFloat(point.value || 0),
          })),
          // Add individual EMA indicators by ID for backward compatibility
          emaByIndicatorId: emaDataByIndicatorId,
          // Simplified MACD processing
          macd: (chartData.indicators?.MACD || chartData.indicators?.macd || []).map((point) => ({
            time: point.time,
            value: parseFloat(point.value || 0),
            signal: parseFloat(point.signal || 0),
            histogram: parseFloat(point.histogram || 0),
          })),
          // Simplified Bollinger Bands processing
          bollingerBands: {
            upper: (chartData.indicators?.BollingerBands?.upper || chartData.indicators?.bollingerBands?.upper || []).map((point) => ({
              time: point.time,
              value: parseFloat(point.value || 0),
            })),
            middle: (chartData.indicators?.BollingerBands?.middle || chartData.indicators?.bollingerBands?.middle || []).map((point) => ({
              time: point.time,
              value: parseFloat(point.value || 0),
            })),
            lower: (chartData.indicators?.BollingerBands?.lower || chartData.indicators?.bollingerBands?.lower || []).map((point) => ({
              time: point.time,
              value: parseFloat(point.value || 0),
            })),
          },

          // Add all indicators by type and ID
          indicatorsByType: indicatorsByType,
          indicatorsByTypeAndId: indicatorsByTypeAndId
        };

        // Debug: Log the EMA indicators being passed to the chart
        console.log("EMA indicators being passed to chart:", {
          emaDataCount: emaData.length,
          emaByIndicatorIdCount: Object.keys(emaDataByIndicatorId).length,
          emaIndicatorIds: Object.keys(emaDataByIndicatorId)
        });

        // Process all indicator types
        commonIndicatorTypes.forEach(type => {
          const lowerType = type.toLowerCase();

          // Add data for each indicator type
          if (indicatorsByType[type] && indicatorsByType[type].length > 0) {
            indicators.indicatorsByType[type] = indicatorsByType[type].map((point) => ({
              time: point.time,
              value: parseFloat(point.value || 0),
            }));
          }

          // Also add with lowercase key for case-insensitive access
          if (indicatorsByType[lowerType] && indicatorsByType[lowerType].length > 0) {
            indicators.indicatorsByType[lowerType] = indicatorsByType[lowerType].map((point) => ({
              time: point.time,
              value: parseFloat(point.value || 0),
            }));
          }

          // Add individual indicators by ID
          if (Object.keys(indicatorsByTypeAndId[type] || {}).length > 0) {
            indicators.indicatorsByTypeAndId[type] = indicatorsByTypeAndId[type];
          }

          // Also add with lowercase key for case-insensitive access
          if (Object.keys(indicatorsByTypeAndId[lowerType] || {}).length > 0) {
            indicators.indicatorsByTypeAndId[lowerType] = indicatorsByTypeAndId[lowerType];
          }
        });

        setCandleData(candles);
        setIndicators(indicators);
      } else {
        setCandleData([]);
        setIndicators({});
      }
    } catch (error) {
      console.error("Error fetching bot data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePauseBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "pause",
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to pause bot");
      }
      setBotStatus("paused");
      setIsRunning(false);
    } catch (err) {
      console.error("Error pausing bot:", err);
      alert("Failed to pause bot: " + (err.message || "Unknown error"));
    } finally {
      setActionLoading(false);
    }
  };

  const handleResumeBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "resume",
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to resume bot");
      }
      setBotStatus("running");
      setIsRunning(true);
    } catch (err) {
      console.error("Error resuming bot:", err);
      alert("Failed to resume bot: " + (err.message || "Unknown error"));
    } finally {
      setActionLoading(false);
    }
  };

  const handleStopBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "stop",
          }),
        }
      );

      // Even if the API returns an error, the bot might still be stopping
      // So we'll check the status after a delay
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.warn("Warning stopping bot:", errorData);
        toast.warning("Bot stop command sent, but there might be issues. Checking status...");

        // Wait a moment and then check the bot status
        setTimeout(() => {
          fetchBotData();
        }, 3000);
      } else {
        toast.success("Bot stopped successfully");
      }

      // Assume the bot is stopping/stopped even if there was an error
      // The fetchBotData call will update the status if needed
      setBotStatus("stopping");
      setIsRunning(false);
    } catch (err) {
      console.error("Error stopping bot:", err);
      toast.error("Failed to stop bot: " + (err.message || "Unknown error"));

      // Still try to fetch the latest status
      setTimeout(() => {
        fetchBotData();
      }, 3000);
    } finally {
      setActionLoading(false);
    }
  };

  const formatTime = (date) => {
    if (!date) return "N/A";
    try {
      let dateToFormat;

      if (date.seconds && date.nanoseconds) {
        dateToFormat = new Date(
          date.seconds * 1000 + date.nanoseconds / 1000000
        );
      } else if (date.toDate) {
        dateToFormat = date.toDate();
      } else if (date instanceof Date) {
        dateToFormat = date;
      } else if (typeof date === "string") {
        dateToFormat = new Date(date.replace("Z", "+00:00"));
      }

      if (!dateToFormat || isNaN(dateToFormat.getTime())) {
        console.warn("Invalid date value:", date);
        return "Invalid Date";
      }

      return dateToFormat.toLocaleString(undefined, {
        timeZone: userTimezone,
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return "Invalid Date";
    }
  };

  const LivePulse = ({ label }) => (
    <div className="flex items-center space-x-2">
      <motion.div
        className="w-2 h-2 bg-green-500 rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [1, 0.5, 1],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <span className="text-sm text-green-400 font-medium">{label}</span>
    </div>
  );

  const tableHeaders = [
    { key: "openTime", label: "Open Time", visible: true },
    { key: "tradeID", label: "Trade ID", visible: false },
    { key: "instrument", label: "Instrument", visible: true },
    { key: "type", label: "Type", visible: true },
    { key: "units", label: "Units", visible: true },
    { key: "price", label: "Entry Price", visible: true },
    { key: "takeProfitPrice", label: "Take Profit Price", visible: false },
    { key: "stopLossPrice", label: "Stop Loss Price", visible: false },
    { key: "initialMarginRequired", label: "Margin", visible: false },
    { key: "halfSpreadCost", label: "Spread Cost", visible: false },
    { key: "commission", label: "Commission", visible: false },
    { key: "realizedPL", label: "Realized P/L", visible: true },
    { key: "unrealizedPL", label: "Unrealized P/L", visible: true },
    { key: "status", label: "Status", visible: true },
    { key: "closeTime", label: "Close Time", visible: true },
  ];

  const [visibleColumns, setVisibleColumns] = useState(
    tableHeaders.reduce((acc, header) => {
      acc[header.key] = header.visible;
      return acc;
    }, {})
  );

  const toggleColumn = (key) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#0c0f1c] to-[#0a0b10] text-white p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Please log in to access the trade bot
          </h1>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#EFBD3A]"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!strategy) {
    return (
      <DashboardLayout>
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Bot not found</h1>
          <button
            onClick={() => router.push("/trade-bots")}
            className="px-4 py-2 bg-[#EFBD3A] text-[#0A0B0B] rounded-lg hover:bg-[#EFBD3A]/90"
          >
            Back to Bots
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 w-full overflow-x-hidden">
        {/* Status Notification */}
        <AnimatePresence>
          {showStatusNotification && (
            <StatusNotification
              status={botStatus}
              previousStatus={previousStatus}
              onClose={() => setShowStatusNotification(false)}
            />
          )}
        </AnimatePresence>

        {/* Trade Notifications */}
        <div className="fixed top-4 right-4 z-50 space-y-2 w-full max-w-md">
          <AnimatePresence>
            {notifications.slice(0, 3).map((notification, index) => (
              <TradeNotification
                key={notification.id}
                notification={notification}
                onClose={() => {
                  const updatedNotifications = notifications.filter(n => n.id !== notification.id);
                  setNotifications(updatedNotifications);
                  notificationsRef.current = updatedNotifications;
                }}
              />
            ))}
          </AnimatePresence>
        </div>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-[#FEFEFF]">
              {strategy.human_readable_rules?.strategy_info?.name ||
                "AI Trading Bot"}
            </h1>
            <p className="text-[#EFBD3A] mt-1">
              {strategy.human_readable_rules?.strategy_info?.instrument ||
                "Unknown"}{" "}
              -{" "}
              {strategy.human_readable_rules?.strategy_info?.timeframe ||
                "Unknown"}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Auto-refresh controls */}
            <div className="flex items-center space-x-4 mr-4 bg-[#1a1a1a] p-2 rounded-lg border border-[#333]">
              <div className="flex items-center space-x-2">
                <label className="text-sm text-gray-400">Auto-refresh:</label>
                <div className="relative inline-block w-10 mr-2 align-middle select-none">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={() => setAutoRefresh(!autoRefresh)}
                    className="sr-only"
                    id="autoRefreshToggle"
                  />
                  <label
                    htmlFor="autoRefreshToggle"
                    className={`block overflow-hidden h-5 rounded-full cursor-pointer transition-colors duration-200 ease-in-out ${autoRefresh ? 'bg-[#EFBD3A]' : 'bg-gray-600'}`}
                  >
                    <span
                      className={`block h-5 w-5 rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out ${autoRefresh ? 'translate-x-5' : 'translate-x-0'}`}
                    ></span>
                  </label>
                </div>
              </div>

              {autoRefresh && (
                <select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  className="bg-[#1a1a1a] border border-[#333] rounded-md px-2 py-1 text-sm text-white focus:outline-none focus:ring-1 focus:ring-[#EFBD3A]"
                >
                  <option value={30000}>30 seconds</option>
                  <option value={60000}>1 minute</option>
                  <option value={300000}>5 minutes</option>
                  <option value={600000}>10 minutes</option>
                  <option value={1800000}>30 minutes</option>
                </select>
              )}

              <button
                onClick={fetchBotData}
                className="bg-[#1a1a1a] hover:bg-[#333] border border-[#333] rounded-md p-1 text-gray-400 hover:text-white focus:outline-none focus:ring-1 focus:ring-[#EFBD3A]"
                title="Refresh data"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>

            <div className="flex items-center space-x-3">
            {/* Action buttons based on status */}
            {botStatus === "running" && (
              <button
                onClick={handlePauseBot}
                disabled={actionLoading}
                className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>Pause Bot</span>
              </button>
            )}

            {botStatus === "paused" && (
              <button
                onClick={handleResumeBot}
                disabled={actionLoading}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>Resume Bot</span>
              </button>
            )}

            {/* Show loading buttons for transition states */}
            {(botStatus === "initializing" || botStatus === "initialized") && (
              <button
                disabled={true}
                className="px-4 py-2 bg-amber-600/50 text-white rounded-lg font-medium flex items-center space-x-2 cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Initializing...</span>
              </button>
            )}

            {botStatus === "pausing" && (
              <button
                disabled={true}
                className="px-4 py-2 bg-yellow-600/50 text-white rounded-lg font-medium flex items-center space-x-2 cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Pausing...</span>
              </button>
            )}

            {botStatus === "resuming" && (
              <button
                disabled={true}
                className="px-4 py-2 bg-green-600/50 text-white rounded-lg font-medium flex items-center space-x-2 cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Resuming...</span>
              </button>
            )}

            {botStatus === "stopping" && (
              <button
                disabled={true}
                className="px-4 py-2 bg-red-600/50 text-white rounded-lg font-medium flex items-center space-x-2 cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5 animate-spin"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <span>Stopping...</span>
              </button>
            )}

            {botStatus === "market_closed" && (
              <button
                disabled={true}
                className="px-4 py-2 bg-purple-600/50 text-white rounded-lg font-medium flex items-center space-x-2 cursor-not-allowed"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>Market Closed</span>
              </button>
            )}

            {/* Stop button for active states */}
            {["running", "paused", "error", "market_closed"].includes(
              botStatus
            ) && (
              <button
                onClick={handleStopBot}
                disabled={actionLoading}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"
                  />
                </svg>
                <span>Stop Bot</span>
              </button>
            )}
          </div>
          </div>
        </div>

        {/* Status Indicator */}
        <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-4 mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  botStatus === "running"
                    ? "bg-green-500 animate-pulse"
                    : botStatus === "paused"
                    ? "bg-yellow-500"
                    : botStatus === "initializing"
                    ? "bg-amber-500 animate-pulse"
                    : botStatus === "initialized"
                    ? "bg-blue-500 animate-pulse"
                    : botStatus === "pausing"
                    ? "bg-yellow-500 animate-pulse"
                    : botStatus === "resuming"
                    ? "bg-green-500 animate-pulse"
                    : botStatus === "stopping"
                    ? "bg-red-500 animate-pulse"
                    : botStatus === "stopped"
                    ? "bg-gray-500"
                    : botStatus === "error"
                    ? "bg-red-500"
                    : botStatus === "market_closed"
                    ? "bg-purple-500"
                    : botStatus === "not_in_session"
                    ? "bg-blue-500"
                    : "bg-gray-500"
                }`}
              />
              <span className="text-[#FEFEFF] font-medium">Status:</span>
              <span
                className={`font-medium ${
                  botStatus === "running"
                    ? "text-green-400"
                    : botStatus === "paused"
                    ? "text-yellow-400"
                    : botStatus === "initializing"
                    ? "text-amber-400"
                    : botStatus === "initialized"
                    ? "text-blue-400"
                    : botStatus === "pausing"
                    ? "text-yellow-400"
                    : botStatus === "resuming"
                    ? "text-green-400"
                    : botStatus === "stopping"
                    ? "text-red-400"
                    : botStatus === "stopped"
                    ? "text-gray-400"
                    : botStatus === "error"
                    ? "text-red-400"
                    : botStatus === "market_closed"
                    ? "text-purple-400"
                    : botStatus === "not_in_session"
                    ? "text-blue-400"
                    : "text-gray-400"
                }`}
              >
                {botStatus === "initializing"
                  ? "INITIALIZING"
                  : botStatus === "initialized"
                  ? "INITIALIZED"
                  : botStatus === "pausing"
                  ? "PAUSING..."
                  : botStatus === "resuming"
                  ? "RESUMING..."
                  : botStatus === "stopping"
                  ? "STOPPING..."
                  : botStatus === "market_closed"
                  ? "MARKET CLOSED"
                  : botStatus === "not_in_session"
                  ? "OUTSIDE TRADING SESSION"
                  : botStatus === "error"
                  ? "ERROR"
                  : botStatus?.toUpperCase()}
              </span>
            </div>
            {lastHeartbeat && (
              <div className="text-gray-400 text-sm">
                Last Update: {formatTime(lastHeartbeat)}
              </div>
            )}
            {marketStatus && (
              <div
                className={`text-sm font-medium ${
                  typeof marketStatus === 'string'
                    ? (marketStatus === "open"
                      ? "text-green-400"
                      : marketStatus === "closed"
                      ? "text-red-400"
                      : "text-yellow-400")
                    : (marketStatus.is_open
                      ? "text-green-400"
                      : "text-red-400")
                }`}
              >
                Market: {typeof marketStatus === 'string' ? marketStatus.toUpperCase() : marketStatus.is_open ? 'OPEN' : 'CLOSED'}
              </div>
            )}
            {botStatus === "error" && (
              <div className="flex-1 mt-2 md:mt-0">
                <div className="text-sm text-red-400 bg-red-900/30 px-3 py-2 rounded-md">
                  <span className="font-semibold">Error:</span> An error
                  occurred with this bot. You can retry or stop the bot.
                </div>
              </div>
            )}
            {botStatus === "market_closed" && (
              <div className="flex-1 mt-2 md:mt-0">
                <div className="text-sm text-purple-400 bg-purple-900/30 px-3 py-2 rounded-md">
                  <span className="font-semibold">Market Closed:</span> The bot
                  is waiting for the market to open before resuming trading.
                </div>
              </div>
            )}
            {botStatus === "not_in_session" && (
              <div className="flex-1 mt-2 md:mt-0">
                <div className="text-sm text-blue-400 bg-blue-900/30 px-3 py-2 rounded-md">
                  <span className="font-semibold">Outside Trading Session:</span> The bot
                  is waiting for the next trading session to begin. Trading will resume automatically
                  when the session starts.
                </div>
              </div>
            )}
          </div>
        </div>



        {/* Strategy Visualization */}
        <div className="mb-6 overflow-x-auto w-full" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-2xl font-bold text-[#FEFEFF]">Strategy Review</h3>
          </div>
          {strategy?.strategy_json && (
            <>
              {console.log("Strategy JSON before passing to StrategyVisualization:", strategy.strategy_json)}
              {console.log("Risk management data before passing to StrategyVisualization:", riskManagement)}
              <StrategyVisualization
                strategy={{
                  ...strategy.strategy_json,
                  riskManagement: {
                    ...strategy.strategy_json.riskManagement,
                    ...riskManagement?.parameters
                  }
                }}
              />
            </>
          )}
          {!strategy?.strategy_json && strategy?.human_readable_rules && (
            <StrategyRules rules={strategy.human_readable_rules} />
          )}
        </div>

        {/* Risk Management Panel */}
        {riskManagement && (
          <RiskManagementPanel
            riskManagement={riskManagement.parameters}
            accountBalance={accountBalance}
            totalProfit={riskManagement.metrics?.totalProfit || 0}
            totalLoss={riskManagement.metrics?.totalLoss || 0}
            dailyLoss={riskManagement.metrics?.dailyLoss || 0}
            metrics={riskManagement.metrics}
          />
        )}

        {/* Chart */}
        <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-4 relative">
          {/* Overlay for paused bot */}
          {botStatus === "paused" && (
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="bg-yellow-600/20 p-4 rounded-lg inline-flex mb-4">
                  <svg
                    className="w-12 h-12 text-yellow-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Bot Paused
                </h3>
                <p className="text-gray-300">
                  Chart updates are paused while the bot is inactive
                </p>
              </div>
            </div>
          )}

          {botStatus === "not_in_session" && (
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="bg-blue-600/20 p-4 rounded-lg inline-flex mb-4">
                  <svg
                    className="w-12 h-12 text-blue-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Outside Trading Session
                </h3>
                <p className="text-gray-300">
                  Chart updates are paused while outside the trading session
                </p>
              </div>
            </div>
          )}

          {/* Hide chart for stopped, market closed, or not in session bot */}
          {botStatus === "stopped" || botStatus === "market_closed" || botStatus === "not_in_session" ? (
            <div className="p-12 text-center">
              <div
                className={`${
                  botStatus === "market_closed"
                    ? "bg-purple-600/20"
                    : botStatus === "not_in_session"
                    ? "bg-blue-600/20"
                    : "bg-gray-700/20"
                } p-4 rounded-lg inline-flex mb-4`}
              >
                <svg
                  className={`w-12 h-12 ${
                    botStatus === "market_closed"
                      ? "text-purple-500"
                      : botStatus === "not_in_session"
                      ? "text-blue-500"
                      : "text-gray-500"
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  {botStatus === "market_closed" ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  ) : botStatus === "not_in_session" ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
                    />
                  )}
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {botStatus === "market_closed"
                  ? "Market Closed"
                  : botStatus === "not_in_session"
                  ? "Outside Trading Session"
                  : "Bot Stopped"}
              </h3>
              <p className="text-gray-300">
                {botStatus === "market_closed"
                  ? "Trading is unavailable while the market is closed"
                  : botStatus === "not_in_session"
                  ? "Trading is paused until the next trading session begins"
                  : "Chart is not available when the bot is stopped"}
              </p>
            </div>
          ) : (
            <OptimizedTradingChart
              candleData={candleData}
              indicators={indicators}
              trades={openTrades}
              timeframe={
                strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"
              }
              chartTimeframe="1h"
              instrument={
                strategy?.human_readable_rules?.strategy_info?.instrument ||
                "EUR/USD"
              }
              marketStatus={marketStatus}
              strategy={strategy}
              strategyInfo={strategy?.human_readable_rules?.strategy_info}
            />
          )}
        </div>

        {/* Trade History */}
        <div className="mt-8 overflow-x-auto w-full" style={{ maxWidth: '100%', overflowX: 'auto' }} onClick={() => isColumnMenuOpen && setIsColumnMenuOpen(false)}>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-300">Trade History <span className="text-sm font-normal text-gray-400 ml-2">({historicalTrades.length} trades)</span></h2>
            <div>
              <button
                ref={columnButtonRef}
                className="columns-button px-4 py-2 bg-[#1a1a1a] text-gray-300 rounded-lg hover:bg-[#2a2a2a] transition-colors flex items-center space-x-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsColumnMenuOpen(!isColumnMenuOpen);
                }}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16m-7 6h7"
                  />
                </svg>
                <span>Customize Columns</span>
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${isColumnMenuOpen ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>





            </div>
          </div>
          <div className="bg-[#0A0B0B] shadow sm:rounded-lg border border-[#1a1a1a] w-full">
            <div className="overflow-x-scroll w-full" style={{ maxWidth: '100%', overflowX: 'scroll', WebkitOverflowScrolling: 'touch' }}>
              <table className="w-full table-fixed divide-y divide-gray-700 border-collapse" style={{ minWidth: '1200px' }}>
                <thead className="bg-[#1a1a1a] sticky top-0 z-10">
                  <tr>
                    {tableHeaders.map(
                      (header) =>
                        visibleColumns[header.key] && (
                          <th
                            key={header.key}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider whitespace-nowrap overflow-hidden text-ellipsis border-b border-gray-700 group"
                            style={{ maxWidth: '150px' }}
                            onClick={() => {
                              // Add sorting functionality here if needed
                            }}
                          >
                            {header.label}
                          </th>
                        )
                    )}
                  </tr>
                </thead>
                <tbody className="bg-[#0A0B0B] divide-y divide-gray-700">
                  {historicalTrades.length === 0 ? (
                    <tr>
                      <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-6 py-8 text-center text-gray-400">
                        No trades found. Trades will appear here when the bot executes them.
                      </td>
                    </tr>
                  ) : (
                    historicalTrades.map((trade) => {
                      return (
                        <tr
                          key={trade.id}
                          className="border-b border-gray-700 hover:bg-gray-800"
                        >
                          {tableHeaders.map(
                            (header) => {
                              if (!visibleColumns[header.key]) return null;

                              return (
                                <td
                                  key={header.key}
                                  className={`px-6 py-4 whitespace-nowrap text-sm overflow-hidden text-ellipsis border-b border-gray-800 ${header.key === 'realizedPL' || header.key === 'unrealizedPL' ?
                                    (trade[header.key] >= 0 ? 'text-green-400' : 'text-red-400') :
                                    'text-gray-300'}`}
                                  style={{ maxWidth: '150px' }}
                                >
                                  {header.key === "type" ? (
                                    <span
                                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        trade.units > 0
                                          ? "bg-green-900/40 text-green-300 border border-green-700/50"
                                          : "bg-red-900/40 text-red-300 border border-red-700/50"
                                      }`}
                                    >
                                      {trade.type === "long" ? "LONG" : "SHORT"}
                                    </span>
                                  ) : header.key === "status" ? (
                                    <span
                                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        trade.status === "OPEN" || trade.status === "open"
                                          ? "bg-blue-900/40 text-blue-300 border border-blue-700/50"
                                          : "bg-gray-800/80 text-gray-300 border border-gray-700/50"
                                      }`}
                                    >
                                      {trade.status.toUpperCase()}
                                    </span>
                                  ) : header.key === "realizedPL" ||
                                    header.key === "unrealizedPL" ? (
                                    <span
                                      className={
                                        trade[header.key] >= 0
                                          ? "text-green-400 font-medium"
                                          : "text-red-400 font-medium"
                                      }
                                    >
                                      {trade[header.key] >= 0 ? '+' : ''}
                                      ${parseFloat(trade[header.key]).toFixed(2)}
                                    </span>
                                  ) : header.key === "openTime" ? (
                                    formatTime(trade.openTime)
                                  ) : header.key === "closeTime" ? (
                                    trade.status === "CLOSED" || trade.status === "closed" ? (
                                      formatTime(trade.closeTime)
                                    ) : (
                                      "N/A"
                                    )
                                  ) : header.key === "units" ? (
                                    Math.abs(trade.units).toLocaleString()
                                  ) : header.key === "price" ||
                                    header.key === "takeProfitPrice" ||
                                    header.key === "stopLossPrice" ? (
                                    trade[header.key]?.toFixed(5) || "N/A"
                                  ) : (
                                    trade[header.key]
                                  )}
                                </td>
                              );
                            }
                          )}
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <PerformanceMetrics metrics={strategySummary} accountBalance={accountBalance} historicalTrades={historicalTrades} openTrades={openTrades} />

        {/* Market Conditions */}
        <MarketConditionsPanel marketStatus={marketStatus} strategy={strategy} />

        {/* User Logs */}
        <UserLogs logs={tradeLogs} />
      </div>

      {/* Column Menu Modal */}
      {isColumnMenuOpen && (
        <div
          className="fixed inset-0 z-[9999]"
          aria-labelledby="column-menu-title"
          role="dialog"
          aria-modal="true"
        >
          {/* Background overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={() => setIsColumnMenuOpen(false)}
          ></div>

          {/* Menu panel */}
          <div
            className="fixed right-4 top-20 w-64 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#2a2a2a] py-2 z-50"
            style={{
              maxHeight: '80vh',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
              overflow: 'hidden'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="px-4 py-2 border-b border-[#2a2a2a] flex justify-between items-center">
              <h3 className="font-medium text-[#EFBD3A]">Table Columns</h3>
              <div className="flex space-x-2">
                <button
                  className="text-xs text-gray-400 hover:text-white transition-colors"
                  onClick={() => {
                    const allVisible = Object.values(visibleColumns).every(v => v);
                    const newState = {};
                    tableHeaders.forEach(h => newState[h.key] = !allVisible);
                    setVisibleColumns(newState);
                  }}
                >
                  {Object.values(visibleColumns).every(v => v) ? 'Hide All' : 'Show All'}
                </button>
                <button
                  className="text-xs text-gray-400 hover:text-white transition-colors"
                  onClick={() => {
                    const defaultState = {};
                    tableHeaders.forEach(h => defaultState[h.key] = h.visible);
                    setVisibleColumns(defaultState);
                  }}
                >
                  Reset
                </button>
              </div>
            </div>
            <div className="max-h-[50vh] overflow-y-auto py-1">
              {tableHeaders.map((header) => (
                <label
                  key={header.key}
                  className="flex items-center px-4 py-2 hover:bg-[#2a2a2a] cursor-pointer transition-colors"
                >
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      id={`column-${header.key}`}
                      checked={visibleColumns[header.key]}
                      onChange={() => toggleColumn(header.key)}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded border ${visibleColumns[header.key] ? 'bg-[#EFBD3A] border-[#EFBD3A]' : 'border-gray-500'} mr-3 flex items-center justify-center transition-colors`}>
                      {visibleColumns[header.key] && (
                        <svg className="w-3 h-3 text-black" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                  <span className="text-gray-300">{header.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}