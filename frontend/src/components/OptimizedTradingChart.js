import React, { useEffect, useRef, useState, useMemo } from "react";
import {
  create<PERSON>hart,
  IChartApi,
  ISeriesApi,
  CrosshairMode,
  LineStyle,
  PriceScaleMode,
} from "lightweight-charts";

export default function OptimizedTradingChart({
  candleData,
  indicators,
  trades,
  timeframe: displayTimeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
}) {
  const chartRef = useRef();
  const chartInstanceRef = useRef();
  const candleSeriesRef = useRef();
  const rsiSeriesRef = useRef();
  const smaSeriesRef = useRef();
  const emaSeriesRef = useRef();
  const overboughtLineRef = useRef();
  const oversoldLineRef = useRef();
  const smaDataRef = useRef([]);
  const rsiDataRef = useRef([]);
  const emaDataRef = useRef([]);

  // References for all indicator types
  const indicatorSeriesRefs = useRef({});
  const indicatorDataRefs = useRef({});

  // For backward compatibility
  const emaSeriesRefsById = useRef({});
  const emaDataRefsById = useRef({});

  const [tooltipData, setTooltipData] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);
  const [showLevels, setShowLevels] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [arePriceLevelsLoaded, setArePriceLevelsLoaded] = useState(false);
  const [inTradingSession, setInTradingSession] = useState(true);

  // Function to analyze indicator dependencies
  const analyzeIndicatorDependencies = () => {
    if (!strategy?.strategy_json?.indicators) return {};

    const dependencies = {};

    // Map indicator IDs to their sources
    strategy.strategy_json.indicators.forEach(indicator => {
      const id = indicator.id;
      const source = indicator.source;

      // If source is not 'price' or a price component (open, high, low, close, volume),
      // it's likely another indicator
      if (source && source !== 'price' &&
          source !== 'open' && source !== 'high' &&
          source !== 'low' && source !== 'close' &&
          source !== 'volume') {
        dependencies[id] = source;
      }
    });

    console.log("Indicator dependencies:", dependencies);
    return dependencies;
  };

  // Get indicator dependencies
  const indicatorDependencies = useMemo(() => analyzeIndicatorDependencies(), [strategy]);

  // Check if current time is in any of the trading sessions
  const checkTradingSession = () => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    const currentHour = new Date().getUTCHours();

    // Check if current time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return currentHour >= 13 && currentHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return currentHour >= 8 && currentHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return currentHour >= 0 && currentHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return currentHour >= 22 || currentHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };
  const lastUpdateRef = useRef(Date.now());
  const isInitialRenderRef = useRef(true);
  const hasInitializedRef = useRef(false);
  const hasProcessedTradesRef = useRef(false);
  const lastCandleCountRef = useRef(0);
  const lastTradeCountRef = useRef(0);

  // Define consistent price format settings at component scope
  const priceFormat = {
    type: "price",
    precision: 5,
    minMove: 0.00001,
  };

  const chartOptions = {
    layout: {
      background: { color: "#1e222d" },
      textColor: "#d1d4dc",
    },
    grid: {
      vertLines: { color: "#2a2e39" },
      horzLines: { color: "#2a2e39" },
    },
    crosshair: {
      mode: CrosshairMode.Magnet,
      vertLine: {
        color: "#758696",
        width: 1,
        style: 1, // LineStyle.Dashed
        visible: true,
        labelVisible: false,
      },
      horzLine: {
        color: "#758696",
        width: 1,
        style: 1, // LineStyle.Dashed
        visible: true,
        labelVisible: true,
      },
    },
    rightPriceScale: {
      borderColor: "#2a2e39",
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
    },
    timeScale: {
      borderColor: "#2a2e39",
      timeVisible: true,
      secondsVisible: false,
    },
  };

  // Define updateChart function at component scope
  const updateChart = () => {
    const chart = chartInstanceRef.current;

    // Ensure price scale settings are maintained
    chart.priceScale("right").applyOptions({
      mode: PriceScaleMode.Normal,
      autoScale: true,
      alignLabels: true,
      borderVisible: true,
      entireTextOnly: false,
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
      priceFormat,
    });

    // Calculate timezone offset once at the start
    const tzOffset = new Date().getTimezoneOffset() * 60;

    // Convert all candle timestamps from UTC to local time at the start
    const localCandleData = candleData.map((candle) => ({
      ...candle,
      time: candle.time - tzOffset,
    }));

    // Disable chart interactions during update
    chart.applyOptions({
      handleScroll: false,
      handleScale: false,
    });

    // Clear existing series
    if (candleSeriesRef.current) {
      chart.removeSeries(candleSeriesRef.current);
      candleSeriesRef.current = null;
    }
    if (rsiSeriesRef.current) {
      chart.removeSeries(rsiSeriesRef.current);
      rsiSeriesRef.current = null;
    }
    if (smaSeriesRef.current) {
      chart.removeSeries(smaSeriesRef.current);
      smaSeriesRef.current = null;
    }
    if (emaSeriesRef.current) {
      chart.removeSeries(emaSeriesRef.current);
      emaSeriesRef.current = null;
    }

    // Remove all EMA series by ID (backward compatibility)
    Object.values(emaSeriesRefsById.current).forEach(series => {
      if (series) {
        chart.removeSeries(series);
      }
    });
    emaSeriesRefsById.current = {};

    // Remove all indicator series by type and ID
    Object.values(indicatorSeriesRefs.current).forEach(typeRefs => {
      Object.values(typeRefs).forEach(series => {
        if (series) {
          chart.removeSeries(series);
        }
      });
    });
    indicatorSeriesRefs.current = {};
    if (overboughtLineRef.current) {
      chart.removeSeries(overboughtLineRef.current);
      overboughtLineRef.current = null;
    }
    if (oversoldLineRef.current) {
      chart.removeSeries(oversoldLineRef.current);
      oversoldLineRef.current = null;
    }

    // Create candlestick series with proper price format
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceFormat,
    });

    candleSeriesRef.current = candlestickSeries;

    // Set candle data
    if (localCandleData.length > 0) {
      candlestickSeries.setData(localCandleData);

      // Add trading session highlighting if sessions are specified
      const tradingSessions = strategy?.strategy_json?.tradingSession || [];
      if (tradingSessions && tradingSessions.length > 0) {
        console.log("[Chart Init] Adding trading session markers");

        // Create session markers for each candle
        const sessionMarkers = [];

        // Group consecutive session candles
        let currentSessionStart = null;
        let inSession = false;

        // Process candles to find session boundaries
        localCandleData.forEach((candle, index) => {
          const isInSession = isTimestampInSession(candle.time);

          // Session start
          if (isInSession && !inSession) {
            currentSessionStart = candle.time;
            inSession = true;

            // Add session marker
            sessionMarkers.push({
              time: candle.time,
              position: 'belowBar',
              color: 'rgba(76, 175, 80, 0.8)',
              shape: 'circle',
              size: 1
            });
          }

          // Session end
          if (!isInSession && inSession) {
            inSession = false;

            // Add session end marker
            sessionMarkers.push({
              time: localCandleData[index - 1]?.time || candle.time,
              position: 'belowBar',
              color: 'rgba(255, 193, 7, 0.8)',
              shape: 'circle',
              size: 1
            });
          }

          // Add background color for session candles
          if (isInSession) {
            // Add a colored marker for each candle in the session
            candlestickSeries.createPriceLine({
              price: 0,
              time: candle.time,
              color: 'rgba(76, 175, 80, 0.1)',
              lineWidth: 2,
              lineStyle: 0,  // Solid
              axisLabelVisible: false,
              title: '',
            });
          }
        });

        // Add markers to the chart
        if (sessionMarkers.length > 0) {
          candlestickSeries.setMarkers(sessionMarkers);
        }
      }
    }

    // Process trades
    let hasPriceLevels = false;
    if (trades && trades.length > 0) {
      console.log("[Trade Processing] Starting trade processing:", {
        tradeCount: trades.length,
        openTrades: trades.filter((t) => t.status === "open" || t.status === "OPEN").length,
      });

      // Find open trades - check for both uppercase and lowercase status
      const openTrades = trades.filter((trade) =>
        trade.status === "open" || trade.status === "OPEN" ||
        (trade.status !== "CLOSED" && trade.status !== "closed" && !trade.closeTime)
      );

      console.log("[Trade Processing] Open trades after filtering:", openTrades);

      if (openTrades.length > 0) {
        console.log("[Trade Processing] Processing open trades:", openTrades);
        hasPriceLevels = true;

        // Process each trade
        openTrades.forEach((trade) => {
          // Convert trade timestamps from UTC to local time
          let startTime, endTime;

          // Get the earliest and latest times from candle data
          const firstCandleTime = localCandleData[0].time;
          const lastCandleTime =
            localCandleData[localCandleData.length - 1].time;

          // Set startTime to either trade open time or first candle time
          if (trade.openTime instanceof Date) {
            startTime = Math.floor(trade.openTime.getTime() / 1000) - tzOffset;
          } else if (typeof trade.openTime === "string") {
            startTime =
              Math.floor(new Date(trade.openTime).getTime() / 1000) - tzOffset;
          } else if (
            typeof trade.openTime === "object" &&
            trade.openTime?.seconds
          ) {
            startTime = trade.openTime.seconds - tzOffset;
          } else if (typeof trade.openTime === "number") {
            startTime = Math.floor(trade.openTime / 1000) - tzOffset;
          }

          // Ensure startTime isn't before first candle
          startTime = Math.max(startTime, firstCandleTime);

          // Set endTime to either trade close time or last candle time
          if (trade.closeTime) {
            if (trade.closeTime instanceof Date) {
              endTime = Math.floor(trade.closeTime.getTime() / 1000) - tzOffset;
            } else if (typeof trade.closeTime === "string") {
              endTime =
                Math.floor(new Date(trade.closeTime).getTime() / 1000) -
                tzOffset;
            } else if (typeof trade.closeTime?.seconds) {
              endTime = trade.closeTime.seconds - tzOffset;
            } else if (typeof trade.closeTime === "number") {
              endTime = Math.floor(trade.closeTime / 1000) - tzOffset;
            }
          } else {
            endTime = lastCandleTime;
          }

          // Ensure endTime isn't after last candle and is after startTime
          endTime = Math.min(endTime, lastCandleTime);
          endTime = Math.max(endTime, startTime + 1); // Ensure at least 1 second difference

          // Add price lines with proper formatting
          console.log("[Trade Processing] Creating entry price line:", {
            price: trade.price,
            type: typeof trade.price
          });

          try {
            candlestickSeries.createPriceLine({
              price: trade.price,
              color: "#EFBD3A",
              lineWidth: 2,
              lineStyle: LineStyle.Dashed,
              axisLabelVisible: true,
              title: `Entry: ${trade.price.toFixed(5)}`,
            });
            console.log("[Trade Processing] Entry price line created successfully");
          } catch (error) {
            console.error("[Trade Processing] Error creating entry price line:", error);
          }

          if (trade.takeProfitPrice) {
            console.log("[Trade Processing] Creating TP price line:", {
              price: trade.takeProfitPrice,
              type: typeof trade.takeProfitPrice
            });

            try {
              candlestickSeries.createPriceLine({
                price: trade.takeProfitPrice,
                color: "#26a69a",
                lineWidth: 2,
                lineStyle: LineStyle.Dotted,
                axisLabelVisible: true,
                title: `TP: ${trade.takeProfitPrice.toFixed(5)}`,
              });
              console.log("[Trade Processing] TP price line created successfully");
            } catch (error) {
              console.error("[Trade Processing] Error creating TP price line:", error);
            }

            // Add profit zone if TP exists
            console.log("Creating profit zone:", {
              type: trade.type,
              entry: trade.price,
              tp: trade.takeProfitPrice,
              startTime,
              endTime,
              expectedZone:
                trade.type === "long"
                  ? `${trade.price} to ${trade.takeProfitPrice} (above entry)`
                  : `${trade.takeProfitPrice} to ${trade.price} (below entry)`,
            });

            const profitArea = chart.addBaselineSeries({
              baseValue: {
                type: "price",
                price: trade.price,
              },
              topLineColor: "rgba(38, 166, 154, 0)",
              topFillColor1: "rgba(38, 166, 154, 0.28)",
              topFillColor2: "rgba(38, 166, 154, 0.05)",
              bottomLineColor: "rgba(38, 166, 154, 0)",
              bottomFillColor1: "rgba(38, 166, 154, 0.05)",
              bottomFillColor2: "rgba(38, 166, 154, 0.28)",
              lineWidth: 0,
              priceLineVisible: false,
              lastValueVisible: false,
              crosshairMarkerVisible: false,
              priceFormat,
            });

            // Create data points for uniform shading, ensuring time order
            const profitZoneData = [
              { time: startTime, value: trade.takeProfitPrice },
              { time: endTime, value: trade.takeProfitPrice },
            ].sort((a, b) => a.time - b.time); // Ensure ascending time order

            console.log("Profit zone data:", {
              basePrice: trade.price,
              targetPrice: trade.takeProfitPrice,
              points: profitZoneData,
            });

            profitArea.setData(profitZoneData);
          }

          if (trade.stopLossPrice) {
            console.log("[Trade Processing] Creating SL price line:", {
              price: trade.stopLossPrice,
              type: typeof trade.stopLossPrice
            });

            try {
              candlestickSeries.createPriceLine({
                price: trade.stopLossPrice,
                color: "#ef5350",
                lineWidth: 2,
                lineStyle: LineStyle.Dotted,
                axisLabelVisible: true,
                title: `SL: ${trade.stopLossPrice.toFixed(5)}`,
              });
              console.log("[Trade Processing] SL price line created successfully");
            } catch (error) {
              console.error("[Trade Processing] Error creating SL price line:", error);
            }

            // Add loss zone if SL exists
            console.log("Creating loss zone:", {
              type: trade.type,
              entry: trade.price,
              sl: trade.stopLossPrice,
              startTime,
              endTime,
              expectedZone:
                trade.type === "long"
                  ? `${trade.stopLossPrice} to ${trade.price} (below entry)`
                  : `${trade.price} to ${trade.stopLossPrice} (above entry)`,
            });

            const lossArea = chart.addBaselineSeries({
              baseValue: {
                type: "price",
                price: trade.price,
              },
              topLineColor: "rgba(239, 83, 80, 0)",
              topFillColor1: "rgba(239, 83, 80, 0.28)",
              topFillColor2: "rgba(239, 83, 80, 0.05)",
              bottomLineColor: "rgba(239, 83, 80, 0)",
              bottomFillColor1: "rgba(239, 83, 80, 0.05)",
              bottomFillColor2: "rgba(239, 83, 80, 0.28)",
              lineWidth: 0,
              priceLineVisible: false,
              lastValueVisible: false,
              crosshairMarkerVisible: false,
              priceFormat,
            });

            // Create data points for uniform shading, ensuring time order
            const lossZoneData = [
              { time: startTime, value: trade.stopLossPrice },
              { time: endTime, value: trade.stopLossPrice },
            ].sort((a, b) => a.time - b.time); // Ensure ascending time order

            console.log("Loss zone data:", {
              basePrice: trade.price,
              targetPrice: trade.stopLossPrice,
              points: lossZoneData,
            });

            lossArea.setData(lossZoneData);
          }
        });
      } else {
        console.log("[Trade Processing] No open trades found");
      }
    } else {
      console.log("[Trade Processing] No trades available");
    }

    // Add SMA indicator
    if (indicators?.sma?.length > 0) {
      // Calculate SMA data with local timestamps
      const smaData = indicators?.sma?.map((point) => ({
        ...point,
        time: point.time - tzOffset,
      })) || [];

      if (smaData.length > 0) {
        // Find SMA indicator in strategy JSON
        const smaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          (ind.type === 'SMA' || ind.indicator_class === 'SMA')
        );

        // Check if SMA has another indicator as its source
        const smaSource = smaIndicator?.source;
        const smaHasIndicatorSource = smaIndicator &&
          indicatorDependencies[smaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === smaSource
        );

        console.log("SMA indicator:", smaIndicator);
        console.log("SMA source:", smaSource);
        console.log("SMA has indicator source:", smaHasIndicatorSource);
        console.log("Source indicator:", sourceIndicator);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If SMA has an indicator source and that source is RSI, use the RSI price scale
        if (smaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log("Placing SMA on RSI chart because RSI is its source");
        }

        // Add SMA series on the appropriate price scale
        const smaSeries = chart.addLineSeries({
          color: "#EFBD3A", // Yellow color for SMA
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId, // Use the determined price scale
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#EFBD3A",
          crosshairMarkerBackgroundColor: "#EFBD3A",
          crosshairMarkerBorderWidth: 2,
        });
        smaSeriesRef.current = smaSeries;

        // Add SMA data
        try {
          // Store the data in our ref for tooltip access
          smaDataRef.current = smaData;
          smaSeries.setData(smaData);
          console.log("SMA data stored for tooltip access, length:", smaData.length);
        } catch (error) {
          console.error("Error setting SMA data:", error);
        }
      }
    }

    // Add EMA indicator - both the default one and any individual ones by ID
    if (indicators?.ema?.length > 0) {
      // Calculate EMA data with local timestamps
      const emaData = indicators?.ema?.map((point) => ({
        ...point,
        time: point.time - tzOffset,
      })) || [];

      if (emaData.length > 0) {
        // Find EMA indicator in strategy JSON
        const emaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          (ind.type === 'EMA' || ind.indicator_class === 'EMA')
        );

        // Check if EMA has another indicator as its source
        const emaSource = emaIndicator?.source;
        const emaHasIndicatorSource = emaIndicator &&
          indicatorDependencies[emaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === emaSource
        );

        console.log("EMA indicator:", emaIndicator);
        console.log("EMA source:", emaSource);
        console.log("EMA has indicator source:", emaHasIndicatorSource);
        console.log("Source indicator:", sourceIndicator);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If EMA has an indicator source and that source is RSI, use the RSI price scale
        if (emaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log("Placing EMA on RSI chart because RSI is its source");
        }

        // Add EMA series on the appropriate price scale
        const emaSeries = chart.addLineSeries({
          color: "#FF6B6B", // Red color for EMA
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId, // Use the determined price scale
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#FF6B6B",
          crosshairMarkerBackgroundColor: "#FF6B6B",
          crosshairMarkerBorderWidth: 2,
        });
        emaSeriesRef.current = emaSeries;

        // Add EMA data
        try {
          // Store the data in our ref for tooltip access
          emaDataRef.current = emaData;
          emaSeries.setData(emaData);
          console.log("EMA data stored for tooltip access, length:", emaData.length);
        } catch (error) {
          console.error("Error setting EMA data:", error);
        }
      }
    }

    // Add individual EMA indicators by ID (for backward compatibility)
    if (indicators?.emaByIndicatorId && Object.keys(indicators.emaByIndicatorId).length > 0) {
      console.log("Processing individual EMA indicators:", Object.keys(indicators.emaByIndicatorId));

      // Define a set of colors for different EMAs
      const emaColors = [
        "#FF6B6B", // Red
        "#4ECDC4", // Teal
        "#FFE66D", // Yellow
        "#6A0572", // Purple
        "#FF9F1C", // Orange
        "#2EC4B6", // Turquoise
        "#E71D36", // Bright Red
        "#011627", // Dark Blue
      ];

      // Process each EMA indicator
      Object.entries(indicators.emaByIndicatorId).forEach(([id, emaInfo], index) => {
        if (!emaInfo.data || emaInfo.data.length === 0) return;

        // Calculate EMA data with local timestamps
        const emaData = emaInfo.data.map((point) => ({
          ...point,
          time: point.time - tzOffset,
        }));

        // Get period from parameters if available
        const period = emaInfo.parameters?.period || 'unknown';

        // Find this specific EMA indicator in strategy JSON
        const emaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === id
        );

        // Check if this EMA has another indicator as its source
        const emaSource = emaIndicator?.source;
        const emaHasIndicatorSource = emaIndicator &&
          indicatorDependencies[emaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === emaSource
        );

        console.log(`EMA ${id} (period: ${period}):`, emaIndicator);
        console.log(`EMA ${id} source:`, emaSource);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If EMA has an indicator source and that source is RSI, use the RSI price scale
        if (emaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log(`Placing EMA ${id} on RSI chart because RSI is its source`);
        }

        // Select a color for this EMA
        const colorIndex = index % emaColors.length;
        const emaColor = emaColors[colorIndex];

        // Add EMA series on the appropriate price scale
        const emaSeries = chart.addLineSeries({
          color: emaColor,
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId,
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: emaColor,
          crosshairMarkerBackgroundColor: emaColor,
          crosshairMarkerBorderWidth: 2,
          title: `EMA(${period})`,
        });

        // Store the series reference
        emaSeriesRefsById.current[id] = emaSeries;

        // Add EMA data
        try {
          // Store the data in our ref for tooltip access
          emaDataRefsById.current[id] = {
            data: emaData,
            color: emaColor,
            period: period,
            id: id
          };
          emaSeries.setData(emaData);
          console.log(`EMA ${id} data stored for tooltip access, length:`, emaData.length);
        } catch (error) {
          console.error(`Error setting EMA ${id} data:`, error);
        }
      });
    }

    // Process all indicator types from the new structure
    if (indicators?.indicatorsByTypeAndId) {
      console.log("Processing all indicator types from new structure");

      // Define a set of colors for different indicators
      const indicatorColors = [
        "#FF6B6B", // Red
        "#4ECDC4", // Teal
        "#FFE66D", // Yellow
        "#6A0572", // Purple
        "#FF9F1C", // Orange
        "#2EC4B6", // Turquoise
        "#E71D36", // Bright Red
        "#011627", // Dark Blue
        "#00A8E8", // Blue
        "#007EA7", // Dark Blue
        "#9C89B8", // Purple
        "#F0A202", // Orange
      ];

      // Initialize the indicator series refs structure
      indicatorSeriesRefs.current = {};
      indicatorDataRefs.current = {};

      // Process each indicator type
      Object.entries(indicators.indicatorsByTypeAndId).forEach(([type, indicatorsById]) => {
        if (!indicatorsById || Object.keys(indicatorsById).length === 0) return;

        console.log(`Processing ${Object.keys(indicatorsById).length} ${type} indicators`);

        // Initialize the refs for this indicator type
        indicatorSeriesRefs.current[type] = {};
        indicatorDataRefs.current[type] = {};

        // Process each indicator of this type
        Object.entries(indicatorsById).forEach(([id, indicatorInfo], index) => {
          if (!indicatorInfo.data || indicatorInfo.data.length === 0) return;

          // Calculate indicator data with local timestamps
          const indicatorData = indicatorInfo.data.map((point) => ({
            ...point,
            time: point.time - tzOffset,
          }));

          // Get parameters for display
          const parameters = indicatorInfo.parameters || {};
          const period = parameters.period || 'unknown';
          const displayName = `${type}(${period})`;

          // Find this specific indicator in strategy JSON
          const indicator = strategy?.strategy_json?.indicators?.find(ind =>
            ind.id === id
          );

          // Check if this indicator has another indicator as its source
          const indicatorSource = indicator?.source;
          const hasIndicatorSource = indicator &&
            indicatorDependencies[indicator.id] !== undefined;

          // Find the source indicator (e.g., RSI)
          const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
            ind.id === indicatorSource
          );

          console.log(`${type} ${id} parameters:`, parameters);
          console.log(`${type} ${id} source:`, indicatorSource);

          // Determine which price scale to use
          let priceScaleId = "right"; // Default to price chart

          // If indicator has an indicator source and that source is RSI, use the RSI price scale
          if (hasIndicatorSource && sourceIndicator &&
              (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
            priceScaleId = "rsi";
            console.log(`Placing ${type} ${id} on RSI chart because RSI is its source`);
          }

          // Special handling for certain indicator types
          if (type.toUpperCase() === 'RSI') {
            priceScaleId = "rsi";
          }

          // Select a color for this indicator
          const colorIndex = index % indicatorColors.length;
          const indicatorColor = indicatorColors[colorIndex];

          // Add indicator series on the appropriate price scale
          const indicatorSeries = chart.addLineSeries({
            color: indicatorColor,
            lineWidth: 2,
            lineStyle: 0, // Solid line
            priceScaleId: priceScaleId,
            priceFormat,
            lastValueVisible: false, // Don't show value on the right
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 8,
            crosshairMarkerBorderColor: indicatorColor,
            crosshairMarkerBackgroundColor: indicatorColor,
            crosshairMarkerBorderWidth: 2,
            title: displayName,
          });

          // Store the series reference
          indicatorSeriesRefs.current[type][id] = indicatorSeries;

          // Add indicator data
          try {
            // Store the data in our ref for tooltip access
            indicatorDataRefs.current[type][id] = {
              data: indicatorData,
              color: indicatorColor,
              parameters: parameters,
              displayName: displayName,
              id: id,
              type: type
            };
            indicatorSeries.setData(indicatorData);
            console.log(`${type} ${id} data stored for tooltip access, length:`, indicatorData.length);
          } catch (error) {
            console.error(`Error setting ${type} ${id} data:`, error);
          }
        });
      });
    }

    // Add RSI and other indicators
    if (indicators?.rsi?.length > 0) {
      // Calculate RSI data with local timestamps
      const rsiData =
        indicators?.rsi?.map((point) => ({
          ...point,
          time: point.time - tzOffset,
        })) || [];



      if (rsiData.length > 0) {
        // Add RSI series with a separate price scale
        const rsiSeries = chart.addLineSeries({
          color: "#2962FF",
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: "rsi",
          lastValueVisible: false, // Don't show value on the right
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#2962FF",
          crosshairMarkerBackgroundColor: "#2962FF",
          crosshairMarkerBorderWidth: 2,
          autoscaleInfoProvider: () => ({
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          }),
        });
        rsiSeriesRef.current = rsiSeries;

        // Configure RSI price scale
        const rsiPriceScale = chart.priceScale("rsi");
        rsiPriceScale.applyOptions({
          scaleMargins: {
            top: 0.7,
            bottom: 0,
          },
          visible: true,
          autoScale: true, // Changed to true to better accommodate SMA when it's on the RSI chart
          alignLabels: true,
          borderVisible: true,
          borderColor: "#2a2e39",
          // Check if we have an SMA indicator with RSI as source
          // If so, don't set a fixed range to allow the scale to adjust
          ...(!strategy?.strategy_json?.indicators?.some(ind =>
            (ind.type === 'SMA' || ind.indicator_class === 'SMA') &&
            ind.source === strategy?.strategy_json?.indicators?.find(i =>
              (i.type === 'RSI' || i.indicator_class === 'RSI')
            )?.id
          ) && {
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          }),
        });

        // Add RSI data
        // Store the data in our ref for tooltip access
        rsiDataRef.current = rsiData;
        rsiSeries.setData(rsiData);
        console.log("RSI data stored for tooltip access, length:", rsiData.length);

        // Add a label to the RSI panel if SMA is using RSI as source
        const hasSmaWithRsiSource = strategy?.strategy_json?.indicators?.some(ind =>
          (ind.type === 'SMA' || ind.indicator_class === 'SMA') &&
          ind.source === strategy?.strategy_json?.indicators?.find(i =>
            (i.type === 'RSI' || i.indicator_class === 'RSI')
          )?.id
        );

        if (hasSmaWithRsiSource) {
          // Add a text marker to indicate SMA is on RSI chart
          rsiSeries.setMarkers([
            {
              time: rsiData[rsiData.length - 1].time,
              position: 'inBar',
              color: 'transparent',
              shape: 'circle',
              text: 'SMA(RSI)',
              size: 0,
              id: 'sma-rsi-label',
              textColor: '#EFBD3A',
              fontSize: 11,
              fontFamily: "'Inter', sans-serif",
              fontStyle: 'normal',
              fontWeight: 'normal',
              backgroundMode: 'all',
              backgroundTransparency: 0.7,
              backgroundColor: '#131722',
              borderColor: '#2a2e39',
              borderWidth: 1,
              padding: 4,
              alignment: 'right',
            }
          ]);
        }

        // Add overbought and oversold lines with local timestamps
        const overboughtData = localCandleData.map((point) => ({
          time: point.time,
          value: 70,
        }));

        const oversoldData = localCandleData.map((point) => ({
          time: point.time,
          value: 30,
        }));

        // Add overbought and oversold lines
        const overboughtLine = chart.addLineSeries({
          color: "#787B86",
          lineWidth: 1,
          lineStyle: LineStyle.Dashed,
          priceScaleId: "rsi",
          axisLabelVisible: false,
          lastValueVisible: false,
          autoscaleInfoProvider: () => ({
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          }),
        });

        const oversoldLine = chart.addLineSeries({
          color: "#787B86",
          lineWidth: 1,
          lineStyle: LineStyle.Dashed,
          priceScaleId: "rsi",
          axisLabelVisible: false,
          lastValueVisible: false,
          autoscaleInfoProvider: () => ({
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          }),
        });

        overboughtLineRef.current = overboughtLine;
        oversoldLineRef.current = oversoldLine;

        // Add text labels for overbought/oversold
        const labelOptions = {
          position: "inBar",
          alignment: "right",
          backgroundColor: "#1e222d",
          borderColor: "#2a2e39",
          borderWidth: 1,
          padding: 4,
          fontSize: 11,
          fontFamily: "'Inter', sans-serif",
        };

        // Add markers for the labels (only at the last visible point)
        const lastPoint = rsiData[rsiData.length - 1];
        if (lastPoint) {
          overboughtLine.setMarkers([
            {
              time: lastPoint.time,
              position: "inBar",
              shape: "square",
              color: "transparent",
              text: "70",
              size: 0,
              ...labelOptions,
            },
          ]);

          oversoldLine.setMarkers([
            {
              time: lastPoint.time,
              position: "inBar",
              shape: "square",
              color: "transparent",
              text: "30",
              size: 0,
              ...labelOptions,
            },
          ]);
        }

        overboughtLine.setData(overboughtData);
        oversoldLine.setData(oversoldData);
      }
    }

    // Set final states based on trade processing results
    if (!trades || trades.length === 0 || hasPriceLevels) {
      console.log("[Loading State] Chart ready to show");
      chart.applyOptions({
        handleScroll: true,
        handleScale: true,
      });
      // Only update loading states if we're currently loading
      if (isDataLoading || !arePriceLevelsLoaded) {
        setIsDataLoading(false);
        setArePriceLevelsLoaded(true);
      }
    } else {
      console.log("[Loading State] Keeping chart hidden - no price levels");
      setIsDataLoading(true);
      setArePriceLevelsLoaded(false);
    }
  };

  // Separate useEffect for chart initialization
  useEffect(() => {
    // Prevent double initialization
    if (
      hasInitializedRef.current ||
      !chartRef.current ||
      chartInstanceRef.current
    ) {
      console.log("[Chart Init] Skip - Already initialized or invalid state:", {
        hasInitialized: hasInitializedRef.current,
        hasChartRef: !!chartRef.current,
        hasChartInstance: !!chartInstanceRef.current,
      });
      return;
    }

    console.log("[Chart Init] Starting chart initialization");

    const chart = createChart(chartRef.current, {
      layout: chartOptions.layout,
      grid: chartOptions.grid,
      crosshair: chartOptions.crosshair,
      rightPriceScale: {
        ...chartOptions.rightPriceScale,
        mode: PriceScaleMode.Normal,
        autoScale: true,
        alignLabels: true,
        borderVisible: true,
        entireTextOnly: false,
        priceFormat,
      },
      timeScale: {
        ...chartOptions.timeScale,
        timeVisible: true,
        secondsVisible: false,
      },
      handleScroll: true,
      handleScale: true,
    });

    // Create initial candlestick series with proper price format
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceFormat,
    });

    candleSeriesRef.current = candlestickSeries;
    chartInstanceRef.current = chart;
    hasInitializedRef.current = true;
    console.log("[Chart Init] Chart instance created");


    // Add crosshair move handler for tooltips
    chart.subscribeCrosshairMove((param) => {
      console.log("Crosshair move event:", {
        hasPoint: !!param.point,
        hasTime: !!param.time,
        hasSeriesPrices: !!param.seriesPrices,
        seriesCount: param.seriesPrices ? param.seriesPrices.size : 0
      });

      if (
        param.point === undefined ||
        !param.time ||
        param.point.x < 0 ||
        param.point.y < 0
      ) {
        setShowTooltip(false);
        return;
      }

      const tooltipInfo = {
        time: param.time,
        indicators: {}
      };

      // Add price data if available
      if (candleSeriesRef.current && param.seriesPrices && param.seriesPrices.has(candleSeriesRef.current)) {
        tooltipInfo.price = param.seriesPrices.get(candleSeriesRef.current);
        console.log("Price data:", tooltipInfo.price);
      }

      // Find SMA value for this timestamp from our stored data
      if (smaSeriesRef.current && smaDataRef.current && smaDataRef.current.length > 0) {
        const smaPoint = smaDataRef.current.find(point => point.time === param.time);
        if (smaPoint) {
          tooltipInfo.indicators.sma = smaPoint.value;
          console.log("Found SMA value from data array:", smaPoint.value);
        }
      }

      // Find EMA value for this timestamp from our stored data
      if (emaSeriesRef.current && emaDataRef.current && emaDataRef.current.length > 0) {
        const emaPoint = emaDataRef.current.find(point => point.time === param.time);
        if (emaPoint) {
          tooltipInfo.indicators.ema = emaPoint.value;
          console.log("Found EMA value from data array:", emaPoint.value);
        }
      }

      // Find EMA values for individual EMAs by ID (backward compatibility)
      if (emaDataRefsById.current && Object.keys(emaDataRefsById.current).length > 0) {
        tooltipInfo.indicators.emaById = {};

        Object.entries(emaDataRefsById.current).forEach(([id, emaInfo]) => {
          const emaPoint = emaInfo.data.find(point => point.time === param.time);
          if (emaPoint) {
            tooltipInfo.indicators.emaById[id] = {
              value: emaPoint.value,
              color: emaInfo.color,
              period: emaInfo.period,
              id: id
            };
            console.log(`Found EMA ${id} value from data array:`, emaPoint.value);
          }
        });
      }

      // Find values for all indicator types
      tooltipInfo.indicators.byType = {};

      if (indicatorDataRefs.current) {
        Object.entries(indicatorDataRefs.current).forEach(([type, indicatorsById]) => {
          if (!indicatorsById || Object.keys(indicatorsById).length === 0) return;

          tooltipInfo.indicators.byType[type] = {};

          Object.entries(indicatorsById).forEach(([id, indicatorInfo]) => {
            const indicatorPoint = indicatorInfo.data.find(point => point.time === param.time);
            if (indicatorPoint) {
              tooltipInfo.indicators.byType[type][id] = {
                value: indicatorPoint.value,
                color: indicatorInfo.color,
                parameters: indicatorInfo.parameters,
                displayName: indicatorInfo.displayName,
                id: id,
                type: type
              };
              console.log(`Found ${type} ${id} value from data array:`, indicatorPoint.value);
            }
          });
        });
      }

      // Find RSI value for this timestamp from our stored data
      if (rsiSeriesRef.current && rsiDataRef.current && rsiDataRef.current.length > 0) {
        const rsiPoint = rsiDataRef.current.find(point => point.time === param.time);
        if (rsiPoint) {
          tooltipInfo.indicators.rsi = rsiPoint.value;
          console.log("Found RSI value from data array:", rsiPoint.value);
        }
      }

      console.log("Final tooltip data:", tooltipInfo);

      // Only show tooltip if we have some data to display
      if (tooltipInfo.price || Object.keys(tooltipInfo.indicators).length > 0) {
        setTooltipData(tooltipInfo);
        setTooltipPosition({ x: param.point.x, y: param.point.y });
        setShowTooltip(true);
      } else {
        setShowTooltip(false);
      }
    });

    // Add click handler to show tooltip when clicking on a point
    chart.subscribeClick((param) => {
      console.log("Chart click event:", param);
      if (param.time && param.point) {
        // Process the same way as crosshair move
        const tooltipInfo = {
          time: param.time,
          indicators: {}
        };

        // Add price data if available
        if (candleSeriesRef.current && param.seriesPrices && param.seriesPrices.has(candleSeriesRef.current)) {
          tooltipInfo.price = param.seriesPrices.get(candleSeriesRef.current);
        }

        // Find SMA value for this timestamp from our stored data
        if (smaSeriesRef.current && smaDataRef.current && smaDataRef.current.length > 0) {
          const smaPoint = smaDataRef.current.find(point => point.time === param.time);
          if (smaPoint) {
            tooltipInfo.indicators.sma = smaPoint.value;
            console.log("Found SMA value from data array (click):", smaPoint.value);
          }
        }

        // Find EMA value for this timestamp from our stored data
        if (emaSeriesRef.current && emaDataRef.current && emaDataRef.current.length > 0) {
          const emaPoint = emaDataRef.current.find(point => point.time === param.time);
          if (emaPoint) {
            tooltipInfo.indicators.ema = emaPoint.value;
            console.log("Found EMA value from data array (click):", emaPoint.value);
          }
        }

        // Find EMA values for individual EMAs by ID (backward compatibility)
        if (emaDataRefsById.current && Object.keys(emaDataRefsById.current).length > 0) {
          tooltipInfo.indicators.emaById = {};

          Object.entries(emaDataRefsById.current).forEach(([id, emaInfo]) => {
            const emaPoint = emaInfo.data.find(point => point.time === param.time);
            if (emaPoint) {
              tooltipInfo.indicators.emaById[id] = {
                value: emaPoint.value,
                color: emaInfo.color,
                period: emaInfo.period,
                id: id
              };
              console.log(`Found EMA ${id} value from data array (click):`, emaPoint.value);
            }
          });
        }

        // Find values for all indicator types
        tooltipInfo.indicators.byType = {};

        if (indicatorDataRefs.current) {
          Object.entries(indicatorDataRefs.current).forEach(([type, indicatorsById]) => {
            if (!indicatorsById || Object.keys(indicatorsById).length === 0) return;

            tooltipInfo.indicators.byType[type] = {};

            Object.entries(indicatorsById).forEach(([id, indicatorInfo]) => {
              const indicatorPoint = indicatorInfo.data.find(point => point.time === param.time);
              if (indicatorPoint) {
                tooltipInfo.indicators.byType[type][id] = {
                  value: indicatorPoint.value,
                  color: indicatorInfo.color,
                  parameters: indicatorInfo.parameters,
                  displayName: indicatorInfo.displayName,
                  id: id,
                  type: type
                };
                console.log(`Found ${type} ${id} value from data array (click):`, indicatorPoint.value);
              }
            });
          });
        }

        // Find RSI value for this timestamp from our stored data
        if (rsiSeriesRef.current && rsiDataRef.current && rsiDataRef.current.length > 0) {
          const rsiPoint = rsiDataRef.current.find(point => point.time === param.time);
          if (rsiPoint) {
            tooltipInfo.indicators.rsi = rsiPoint.value;
            console.log("Found RSI value from data array (click):", rsiPoint.value);
          }
        }

        // Show tooltip if we have data
        if (tooltipInfo.price || Object.keys(tooltipInfo.indicators).length > 0) {
          setTooltipData(tooltipInfo);
          setTooltipPosition({ x: param.point.x, y: param.point.y });
          setShowTooltip(true);
        }
      }
    });

    // Handle window resize
    const handleResize = () => {
      if (chartRef.current && chartInstanceRef.current) {
        const { width, height } = chartRef.current.getBoundingClientRect();
        chartInstanceRef.current.applyOptions({ width, height });
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    // Initial data update if data is available
    if (candleData?.length > 0) {
      console.log("[Chart Init] Initial data update");
      updateChart();
    }

    // Cleanup function
    return () => {
      window.removeEventListener("resize", handleResize);
      if (chartInstanceRef.current) {
        console.log("[Chart Cleanup] Starting cleanup");
        try {
          // Remove all series first
          if (candleSeriesRef.current) {
            chartInstanceRef.current.removeSeries(candleSeriesRef.current);
            candleSeriesRef.current = null;
          }
          if (rsiSeriesRef.current) {
            chartInstanceRef.current.removeSeries(rsiSeriesRef.current);
            rsiSeriesRef.current = null;
          }
          if (smaSeriesRef.current) {
            chartInstanceRef.current.removeSeries(smaSeriesRef.current);
            smaSeriesRef.current = null;
          }
          if (emaSeriesRef.current) {
            chartInstanceRef.current.removeSeries(emaSeriesRef.current);
            emaSeriesRef.current = null;
          }

          // Remove all EMA series by ID (backward compatibility)
          Object.values(emaSeriesRefsById.current).forEach(series => {
            if (series) {
              chartInstanceRef.current.removeSeries(series);
            }
          });
          emaSeriesRefsById.current = {};

          // Remove all indicator series by type and ID
          Object.values(indicatorSeriesRefs.current).forEach(typeRefs => {
            Object.values(typeRefs).forEach(series => {
              if (series) {
                chartInstanceRef.current.removeSeries(series);
              }
            });
          });
          indicatorSeriesRefs.current = {};
          if (overboughtLineRef.current) {
            chartInstanceRef.current.removeSeries(overboughtLineRef.current);
            overboughtLineRef.current = null;
          }
          if (oversoldLineRef.current) {
            chartInstanceRef.current.removeSeries(oversoldLineRef.current);
            oversoldLineRef.current = null;
          }

          // Remove the chart instance
          chartInstanceRef.current.remove();
          chartInstanceRef.current = null;
          hasInitializedRef.current = false;
          console.log(
            "[Chart Cleanup] Chart instance removed and refs cleared"
          );
        } catch (error) {
          console.error("[Chart Cleanup] Error during cleanup:", error);
        }
      }
    };
  }, []); // Empty dependency array to ensure single initialization

  // Separate useEffect for data updates
  useEffect(() => {
    // Skip if no chart instance or no data
    if (!chartInstanceRef.current || !candleData?.length) {
      console.log("[Data Update] Missing data or chart instance:", {
        hasCandleData: !!candleData?.length,
        hasChartInstance: !!chartInstanceRef.current,
      });
      return;
    }

    // Skip if not initialized
    if (!hasInitializedRef.current) {
      console.log("[Data Update] Chart not initialized yet");
      return;
    }

    const now = Date.now();
    // Only apply debounce if we have the same data as before
    if (
      !isInitialRenderRef.current &&
      now - lastUpdateRef.current < 1000 && // Reduced debounce time
      candleData?.length === lastCandleCountRef.current &&
      trades?.length === lastTradeCountRef.current
    ) {
      console.log(
        "[Data Update] Skipping update due to debounce - no new data"
      );
      return;
    }

    // Update our tracking refs
    isInitialRenderRef.current = false;
    lastUpdateRef.current = now;
    lastCandleCountRef.current = candleData?.length || 0;
    lastTradeCountRef.current = trades?.length || 0;

    // Start loading state
    setIsDataLoading(true);
    setArePriceLevelsLoaded(false);

    // Update chart with a small delay to ensure proper initialization
    const timeoutId = setTimeout(() => {
      updateChart();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [candleData, trades]); // Remove indicators from dependencies to reduce updates

  // Reset processed trades ref when component unmounts
  useEffect(() => {
    return () => {
      hasProcessedTradesRef.current = false;
    };
  }, []);

  // Helper function to determine if a timestamp is in a trading session
  const isTimestampInSession = (timestamp) => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    // Convert timestamp to Date object and get UTC hours
    const date = new Date(timestamp * 1000);
    const utcHour = date.getUTCHours();

    // Check if time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return utcHour >= 13 && utcHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return utcHour >= 8 && utcHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return utcHour >= 0 && utcHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return utcHour >= 22 || utcHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  // Check trading session status every minute
  useEffect(() => {
    // Initial check
    setInTradingSession(checkTradingSession());

    // Set up interval to check every minute
    const interval = setInterval(() => {
      setInTradingSession(checkTradingSession());
    }, 60000); // 60 seconds

    return () => clearInterval(interval);
  }, [strategy]);

  const isLoading = isDataLoading || !arePriceLevelsLoaded;

  return (
    <div className="relative w-full h-full min-h-[600px]">
      {/* Strategy Info and Market Status Bar */}
      <div className="absolute top-2 left-2 right-2 z-10 flex items-center justify-between">
        {/* Strategy Info */}
        <div className="flex items-center space-x-4">
          <div className="px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm">
            <span className="text-sm font-medium text-[#FEFEFF]">
              {strategyInfo?.name || "Strategy"}
            </span>
          </div>
          <div className="px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center space-x-2">
            <span className="text-sm font-medium text-[#FEFEFF]">
              {instrument}
            </span>
            <span className="text-xs text-[#EFBD3A]">
              {displayTimeframe || strategyInfo?.timeframe}
            </span>
          </div>
        </div>

        {/* Market Status and Trading Session Indicators */}
        <div className="flex items-center space-x-2">
          {/* Trading Session Indicator */}
          {strategy?.strategy_json?.tradingSession && strategy.strategy_json.tradingSession.length > 0 && (
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full backdrop-blur-sm ${inTradingSession ? 'bg-green-900/30 border border-green-500/30' : 'bg-yellow-900/30 border border-yellow-500/30'}`}>
              <div className={`w-2 h-2 rounded-full ${inTradingSession ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'}`}></div>
              <span className={`text-xs font-medium ${inTradingSession ? 'text-green-400' : 'text-yellow-400'}`}>
                {inTradingSession ? 'In Trading Session' : 'Outside Session'}
              </span>
            </div>
          )}

          {/* Market Status Indicator */}
          {marketStatus && (
            <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm">
            <div
              className={`w-2 h-2 rounded-full ${
                marketStatus.is_open ? "bg-[#EFBD3A]" : "bg-red-500"
              }`}
            />
            <span className="text-sm font-medium text-[#FEFEFF]">
              {marketStatus.is_open ? "Market Open" : "Market Closed"}
            </span>
            <span className="text-xs text-[#EFBD3A]">
              {(() => {
                if (!marketStatus.last_updated) return "N/A";
                if (marketStatus.last_updated.seconds) {
                  return new Date(
                    marketStatus.last_updated.seconds * 1000
                  ).toLocaleTimeString();
                }
                const timestamp = marketStatus.last_updated;
                if (typeof timestamp === "number") {
                  return new Date(timestamp).toLocaleTimeString();
                }
                return "N/A";
              })()}
            </span>
          </div>
        )}
        </div>
      </div>

      {/* Loading Overlay - Render on top */}
      {isLoading && (
        <div className="absolute inset-0 bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="flex flex-col items-center space-y-4 bg-[#0A0B0B]/40 p-8 rounded-lg backdrop-blur-sm relative overflow-hidden">
            {/* Animated gradient background */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#0A0B0B]/0 via-[#EFBD3A]/10 to-[#0A0B0B]/0 -skew-x-12 animate-shimmer" />

            {/* Main spinner */}
            <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin relative">
              {/* Inner spinner */}
              <div className="absolute inset-1 border-3 border-[#EFBD3A]/30 border-t-transparent rounded-full animate-spin-slow" />
            </div>

            {/* Loading text with animated dots */}
            <div className="flex flex-col items-center space-y-2">
              <span className="text-lg font-medium text-[#FEFEFF] relative">
                Loading chart data
                <span className="animate-ellipsis">.</span>
                <span className="animate-ellipsis animation-delay-300">.</span>
                <span className="animate-ellipsis animation-delay-600">.</span>
              </span>
              <span className="text-sm text-[#FEFEFF]/70 animate-pulse">
                {!arePriceLevelsLoaded
                  ? "Waiting for price levels..."
                  : "Finalizing..."}
              </span>
            </div>

            {/* Progress bar */}
            <div className="w-48 h-1 bg-[#1a1a1a] rounded-full overflow-hidden mt-4">
              <div className="h-full bg-[#EFBD3A] animate-progress rounded-full" />
            </div>
          </div>
        </div>
      )}

      {/* Chart Container - Always render but control visibility */}
      <div className="relative w-full h-full min-h-[600px]">
        <div
          ref={chartRef}
          className="w-full h-full min-h-[600px] bg-[#0A0B0B]"
          style={{
            opacity: isLoading ? 0.5 : 1,
            transition: "opacity 0.3s ease-in-out",
          }}
        />

        {/* Tooltip */}
        {showTooltip && tooltipData && (
          <div
            className="absolute z-50 bg-[#131722] border-2 border-[#2a2e39] rounded-md shadow-lg p-3 pointer-events-none"
            style={{
              left: Math.min(tooltipPosition.x + 15, window.innerWidth - 220),
              top: Math.min(tooltipPosition.y - 20, window.innerHeight - 200),
              maxWidth: '220px',
              minWidth: '180px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              transition: 'opacity 0.1s ease-in-out',
              opacity: 0.95
            }}
          >
            {/* Timestamp */}
            {tooltipData.time && (
              <div className="mb-2 text-center">
                <div className="text-[#787b86] text-xs">
                  {new Date(tooltipData.time * 1000).toLocaleString()}
                </div>
              </div>
            )}

            {/* Price data */}
            {tooltipData.price && (
              <div className="mb-2">
                <div className="text-[#d1d4dc] text-xs font-medium mb-1">Price</div>
                {typeof tooltipData.price === 'object' ? (
                  <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
                    <div className="text-[#787b86]">Open:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.open?.toFixed(5)}</div>
                    <div className="text-[#787b86]">High:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.high?.toFixed(5)}</div>
                    <div className="text-[#787b86]">Low:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.low?.toFixed(5)}</div>
                    <div className="text-[#787b86]">Close:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.close?.toFixed(5)}</div>
                  </div>
                ) : (
                  <div className="text-[#d1d4dc] font-medium">{tooltipData.price?.toFixed(5)}</div>
                )}
              </div>
            )}

            {/* Indicator data */}
            {(tooltipData.indicators?.sma !== undefined || tooltipData.indicators?.ema !== undefined || tooltipData.indicators?.rsi !== undefined) && (
              <div className="mt-3 pt-3 border-t border-[#2a2e39]">
                <div className="text-[#d1d4dc] text-xs font-medium mb-2">Indicators</div>

                {/* SMA data */}
                {tooltipData.indicators?.sma !== undefined && (
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-[#EFBD3A] mr-2"></div>
                      <div className="text-[#d1d4dc] text-xs">
                        {/* Check if SMA has RSI as source */}
                        {strategy?.strategy_json?.indicators?.find(ind =>
                          (ind.type === 'SMA' || ind.indicator_class === 'SMA') &&
                          ind.source === strategy?.strategy_json?.indicators?.find(i =>
                            (i.type === 'RSI' || i.indicator_class === 'RSI')
                          )?.id
                        ) ? 'SMA(RSI)' : 'SMA'}
                      </div>
                    </div>
                    <div className="text-[#EFBD3A] font-medium text-sm">{tooltipData.indicators.sma.toFixed(5)}</div>
                  </div>
                )}

                {/* EMA data */}
                {tooltipData.indicators?.ema !== undefined && (
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-[#FF6B6B] mr-2"></div>
                      <div className="text-[#d1d4dc] text-xs">
                        {/* Check if EMA has RSI as source */}
                        {strategy?.strategy_json?.indicators?.find(ind =>
                          (ind.type === 'EMA' || ind.indicator_class === 'EMA') &&
                          ind.source === strategy?.strategy_json?.indicators?.find(i =>
                            (i.type === 'RSI' || i.indicator_class === 'RSI')
                          )?.id
                        ) ? 'EMA(RSI)' : 'EMA'}
                      </div>
                    </div>
                    <div className="text-[#FF6B6B] font-medium text-sm">{tooltipData.indicators.ema.toFixed(5)}</div>
                  </div>
                )}

                {/* Individual EMA data by ID (backward compatibility) */}
                {tooltipData.indicators?.emaById && Object.keys(tooltipData.indicators.emaById).length > 0 && (
                  <>
                    {Object.entries(tooltipData.indicators.emaById).map(([id, emaInfo]) => (
                      <div key={id} className="flex justify-between items-center mb-2">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: emaInfo.color }}></div>
                          <div className="text-[#d1d4dc] text-xs">
                            EMA({emaInfo.period})
                          </div>
                        </div>
                        <div className="font-medium text-sm" style={{ color: emaInfo.color }}>
                          {emaInfo.value.toFixed(5)}
                        </div>
                      </div>
                    ))}
                  </>
                )}

                {/* All indicator types */}
                {tooltipData.indicators?.byType && Object.keys(tooltipData.indicators.byType).length > 0 && (
                  <>
                    {Object.entries(tooltipData.indicators.byType).map(([type, indicatorsById]) => (
                      Object.keys(indicatorsById).length > 0 && (
                        <React.Fragment key={type}>
                          {Object.entries(indicatorsById).map(([id, indicatorInfo]) => (
                            <div key={`${type}-${id}`} className="flex justify-between items-center mb-2">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: indicatorInfo.color }}></div>
                                <div className="text-[#d1d4dc] text-xs">
                                  {indicatorInfo.displayName}
                                </div>
                              </div>
                              <div className="font-medium text-sm" style={{ color: indicatorInfo.color }}>
                                {indicatorInfo.value.toFixed(5)}
                              </div>
                            </div>
                          ))}
                        </React.Fragment>
                      )
                    ))}
                  </>
                )}

                {/* RSI data */}
                {tooltipData.indicators?.rsi !== undefined && (
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-[#2962FF] mr-2"></div>
                      <div className="text-[#d1d4dc] text-xs">RSI</div>
                    </div>
                    <div className="text-[#2962FF] font-medium text-sm">{tooltipData.indicators.rsi.toFixed(2)}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) skewX(-12deg);
          }
          100% {
            transform: translateX(200%) skewX(-12deg);
          }
        }
        @keyframes progress {
          0% {
            width: 0%;
          }
          50% {
            width: 70%;
          }
          100% {
            width: 98%;
          }
        }
        @keyframes ellipsis {
          0% {
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0;
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
        .animate-progress {
          animation: progress 3s ease-in-out infinite;
        }
        .animate-ellipsis {
          animation: ellipsis 1s infinite;
          opacity: 0;
        }
        .animation-delay-300 {
          animation-delay: 0.3s;
        }
        .animation-delay-600 {
          animation-delay: 0.6s;
        }
        .animate-spin-slow {
          animation: spin 2s linear infinite;
        }
      `}</style>
    </div>
  );
}
