import os
import logging
import firebase_admin
from firebase_admin import credentials, firestore
from typing import Dict, List, Optional, Union, Any
import json
import time
from datetime import datetime, timezone
from google.cloud.firestore import Increment, ArrayUnion, ArrayRemove, SERVER_TIMESTAMP  # type: ignore
from utils.logger import Logger
import requests
from utils.oanda_utils import get_oanda_url
from core.trading_engine_types import TradeHistoryRow, TradeStatus
from enum import Enum
class FirebaseClient:
    """Client for Firebase operations."""

    def __init__(self, user_id=None, strategy_id=None):
        """Initialize Firebase client.

        Args:
            user_id: The user ID.
            strategy_id: The strategy ID.
        """
        self.user_id = user_id
        self.strategy_id = strategy_id
        self._initialized = False
        self.logger = Logger("FirebaseClient")

        # Performance metrics
        self.metrics = {
            'operations': 0,
            'execution_times': {}
        }

        try:
            # Check if we're using Firebase emulator
            use_emulator = os.getenv('USE_FIREBASE_EMULATOR', 'false').lower() == 'true'

            # Initialize Firebase app if not already initialized
            if not firebase_admin._apps:
                if use_emulator:
                    self.logger.log_info("Initializing Firebase app in emulator mode")
                    # No need for credentials file when using emulator
                    try:
                        firebase_admin.initialize_app()
                    except Exception as e:
                        self.logger.log_error(e, "Error initializing Firebase app in emulator mode")
                        raise
                else:
                    # Production mode - requires credentials file
                    self.logger.log_info("Initializing Firebase app in production mode")
                    cred_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', './firebase-key.json')
                    try:
                        # Check if credential file exists
                        if not os.path.exists(cred_path):
                            self.logger.log_error(f"Credentials file not found: {cred_path}")
                            raise FileNotFoundError(f"Credentials file not found: {cred_path}")

                        cred = credentials.Certificate(cred_path)
                        firebase_admin.initialize_app(cred)
                    except Exception as e:
                        self.logger.log_error(e, "Error initializing Firebase app")
                        raise

            self.logger.log_info("Firebase app initialized")
            self.db = firestore.client()
            self._initialized = True
        except Exception as e:
            self.logger.log_error(e, "Failed to initialize Firebase client")
            raise

    def update_chart_data(self, candle_data: List[Dict], indicators: Dict) -> None:
        """Update chart data in Firestore."""
        try:
            self.logger.log_info(f"Updating chart data for user {self.user_id}, strategy {self.strategy_id}")
            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            # Get current data
            doc = strategy_ref.get()
            current_data = doc.to_dict() if doc.exists else {}

            # Update only the chartData field
            current_data["chartData"] = {
                "candles": candle_data,
                "indicators": indicators,
                "lastUpdated": datetime.now(timezone.utc)
            }

            # Update the document with the merged data
            strategy_ref.set(current_data)

            self.logger.log_info("Chart data updated successfully")
            self.logger.log_info(f"Updated with {len(candle_data)} candles and indicators: {list(indicators.keys())}")

            # Log indicator types to help with debugging
            indicator_types = [key for key in indicators.keys() if not key.startswith('1')]
            if indicator_types:
                self.logger.log_info(f"Indicator types available: {indicator_types}")
        except Exception as e:
            self.logger.log_error(e, "Error updating chart data")
            raise

    def add_to_trade_history(self, trade: TradeHistoryRow) -> None:
        """Add a new trade to trade history in Firebase."""
        try:
            self.logger.log_info(f"Adding trade to history for user {self.user_id}, strategy {self.strategy_id}")
            self.logger.log_info(f"Received trade data: {trade}")

            # Helper function to safely convert to float
            def safe_float(value, default=0.0):
                return float(value) if value is not None else default

            trade_data = {
                "tradeID": trade.tradeID,
                "openTime": trade.openTime,
                "type": trade.type.value,
                "price": float(trade.price),
                "units": abs(float(trade.units)),
                "unrealizedPL": safe_float(trade.unrealizedPL),
                "status": trade.status.value,
                "initialMarginRequired": float(trade.initialMarginRequired),
                "halfSpreadCost": safe_float(trade.halfSpreadCost),
                "commission": safe_float(trade.commission),
                "instrument": trade.instrument
            }

            if trade.closeTime:
                trade_data["closeTime"] = trade.closeTime

            # Add realizedPL if available
            trade_data["realizedPL"] = safe_float(trade.realizedPL)

            # Add take profit and stop loss prices if available
            if hasattr(trade, 'takeProfitPrice') and trade.takeProfitPrice is not None:
                trade_data["takeProfitPrice"] = float(trade.takeProfitPrice)

            if hasattr(trade, 'stopLossPrice') and trade.stopLossPrice is not None:
                trade_data["stopLossPrice"] = float(trade.stopLossPrice)

            # Add trade to history collection using tradeID as document name
            self.db.collection('users').document(self.user_id)\
                .collection('submittedStrategies').document(self.strategy_id)\
                .collection('tradeHistory').document(trade_data['tradeID']).set(trade_data)

            self.logger.log_info("Trade history updated successfully")

        except Exception as e:
            self.logger.log_error(e, "Error adding trade to history")
            self.logger.log_error(f"Error type: {type(e)}")
            self.logger.log_error(f"Error details: {e.__dict__}")
            raise

    def _update_strategy_summary(self, strategy_ref, trade_data: Dict) -> None:
        """Update strategy summary with trade data."""
        try:
            summary_ref = strategy_ref.collection("summary").document("current")
            summary_doc = summary_ref.get()

            if summary_doc.exists:
                summary_data = summary_doc.to_dict()
                total_trades = summary_data.get("totalTrades", 0) + 1
                total_realized_pl = summary_data.get("totalRealizedPL", 0.0) + float(trade_data["realizedPL"])
                total_unrealized_pl = summary_data.get("totalUnrealizedPL", 0.0) + float(trade_data["unrealizedPL"])
                win_rate = summary_data.get("winRate", 0.0)

                # Update win rate if trade is closed
                if trade_data.get("status") == "closed":
                    winning_trades = summary_data.get("winningTrades", 0)
                    if float(trade_data["realizedPL"]) > 0:
                        winning_trades += 1
                    win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0.0

                summary_ref.update({
                    "totalTrades": total_trades,
                    "totalRealizedPL": total_realized_pl,
                    "totalUnrealizedPL": total_unrealized_pl,
                    "winRate": win_rate,
                    "lastUpdated": SERVER_TIMESTAMP
                })
                self.logger.log_info(f"Updated strategy summary: {total_trades} trades, {win_rate:.2f}% win rate")
            else:
                # Create new summary document
                summary_ref.set({
                    "totalTrades": 1,
                    "totalRealizedPL": float(trade_data["realizedPL"]),
                    "totalUnrealizedPL": float(trade_data["unrealizedPL"]),
                    "winRate": 0.0,
                    "winningTrades": 0,
                    "lastUpdated": SERVER_TIMESTAMP
                })
                self.logger.log_info("Created new strategy summary document")
        except Exception as e:
            self.logger.log_error(e, "Error updating strategy summary")
            raise

    def update_account_balance(self, balance: float) -> None:
        """Update account balance in Firestore."""
        try:
            self.logger.log_info(f"Updating account balance for user {self.user_id}, strategy {self.strategy_id}")
            self.logger.log_info(f"New balance value: {balance}")

            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            # Update the document with the balance
            strategy_ref.update({
                "accountBalance": {
                    "balance": float(balance),
                    "lastUpdated": SERVER_TIMESTAMP
                }
            })

            self.logger.log_info(f"Successfully updated account balance to: {balance}")
        except Exception as e:
            self.logger.log_error(e, "Error updating account balance")
            raise

    def update_risk_management(self, risk_management_data: Dict) -> None:
        """Update risk management data in Firestore.

        Args:
            risk_management_data (Dict): Risk management data to update
        """
        try:
            self.logger.log_info(f"Updating risk management data for user {self.user_id}, strategy {self.strategy_id}")

            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            # Update the document with risk management data
            strategy_ref.update({
                "riskManagement": risk_management_data
            })

            self.logger.log_info("Successfully updated risk management data")
        except Exception as e:
            self.logger.log_error(e, "Error updating risk management data")
            # Don't raise the exception to avoid interrupting the trading process

    class BotStatus(Enum):
        INITIALIZING = "initializing"
        INITIALIZED = "initialized"
        PAUSED = "paused"
        PAUSING = "pausing"
        RESUMING = "resuming"
        RUNNING = "running"
        STOPPING = "stopping"
        STOPPED = "stopped"
        ERROR = "error"
        MARKET_CLOSED = "market_closed"
        NOT_IN_SESSION = "not_in_session"
    def update_bot_status(self, status: BotStatus, details: Optional[Dict] = None) -> None:
        """Update bot status in Firestore."""
        try:
            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            # Update status and details
            update_data = {
                "status": status.value,
                "lastHeartbeat": datetime.now(timezone.utc),
                "details": details or {}
            }

            strategy_ref.update(update_data)
            self.logger.log_info(f"Updated bot status to: {status}")
        except Exception as e:
            self.logger.log_error(e, "Error updating bot status")
            raise

    def append_user_log(self, message: str) -> None:
        """Append a log message to the strategy's logs."""
        try:
            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            # Get current data
            doc = strategy_ref.get()
            if not doc.exists:
                return

            current_data = doc.to_dict()
            current_user_logs = current_data.get("user_logs", [])

            # Create new log entry
            new_log = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": message,
            }

            # Append to appropriate log array
            current_user_logs.append(new_log)

            # Keep only last 100 user logs
            current_user_logs = current_user_logs[-100:]

            # Update Firestore
            strategy_ref.update({
                "user_logs": current_user_logs
            })
            self.logger.log_info(f"Appended user log message: {message}")
        except Exception as e:
            self.logger.log_error(e, "Error appending user log")
            raise

    def update_market_status(self, status: Dict[str, Any]) -> None:
        """Update the market status in Firebase.

        Args:
            status (Dict[str, Any]): Market status information
        """
        try:
            if not self._initialized:
                self.logger.log_warning("Firebase not initialized, skipping update_market_status")
                return

            if not self.strategy_id or not self.user_id:
                self.logger.log_warning("No user ID or strategy ID provided, skipping update_market_status")
                return

            # Create a simplified version of the market status for Firebase
            simplified_status = {
                "is_open": status.get("market_hours", {}).get("is_open", False),
                "is_safe_to_trade": status.get("is_safe_to_trade", False),
                "reason": status.get("reason", ""),
                "active_centers": status.get("market_hours", {}).get("active_centers", []),
                "market_activity": status.get("market_hours", {}).get("market_activity", "unknown"),
                "spread_in_pips": status.get("spread_info", {}).get("spread_in_pips", 0),
                "spread_acceptable": status.get("spread_info", {}).get("is_acceptable", True),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            strategy_ref.update({
                "marketStatus": simplified_status,
                "last_updated": SERVER_TIMESTAMP
            })

            self.logger.log_info(f"Updated market status in Firebase: {simplified_status}")
        except Exception as e:
            self.logger.log_error(e, "Failed to update market status in Firebase")

    def get_oanda_credentials(self) -> Dict[str, Any]:
        """Get OANDA credentials from Firestore.

        Returns:
            Dict[str, Any]: OANDA credentials including api_key, account_id, and base_url
        """
        try:
            # Get user ID from environment
            user_id = os.getenv("USER_ID")
            if not user_id:
                raise ValueError("USER_ID environment variable not set")

            self.logger.log_info(f"Fetching OANDA credentials for user {user_id}")

            # Get user document
            user_ref = self.db.collection("users").document(user_id)
            user_doc = user_ref.get()

            if not user_doc.exists:
                error = ValueError(f"User {user_id} not found in Firestore")
                self.logger.log_error(error, "Failed to get OANDA credentials")
                raise error

            user_data = user_doc.to_dict()

            # Check for required fields
            if not user_data.get("api_key"):
                error = ValueError(f"OANDA API key not found for user {user_id}")
                self.logger.log_error(error, "Failed to get OANDA credentials")
                raise error

            # Get account ID from OANDA API
            headers = {
                "Authorization": f"Bearer {user_data['api_key']}",
                "Content-Type": "application/json"
            }

            # Get OANDA base URL based on practice or live environment
            base_url = get_oanda_url()
            response = requests.get(f"{base_url}/accounts", headers=headers)
            response.raise_for_status()

            accounts = response.json().get("accounts", [])
            if not accounts:
                error = ValueError("No OANDA accounts found for this API key")
                self.logger.log_error(error, "Failed to get OANDA credentials")
                raise error

            return {
                "api_key": user_data["api_key"],
                "account_id": accounts[0]["id"],
            }

        except Exception as e:
            self.logger.log_error(e, "Failed to get OANDA credentials")
            raise

    def update_human_readable_rules(self, rules: Dict[str, Any]) -> None:
        """
        Update the human-readable rules in the strategy document.

        Args:
            rules (Dict[str, Any]): Human-readable rules to save
        """
        try:
            self.logger.log_info(f"Updating human-readable rules for user {self.user_id}, strategy {self.strategy_id}")

            strategy_ref = (self.db.collection("users")
                          .document(self.user_id)
                          .collection("submittedStrategies")
                          .document(self.strategy_id))

            strategy_ref.update({
                "human_readable_rules": rules["human_readable_rules"],
                "last_updated": SERVER_TIMESTAMP
            })

            self.logger.log_info("Human-readable rules updated successfully")

        except Exception as e:
            self.logger.log_error(e, "Failed to update human-readable rules")
            raise

    def _prepare_trade_data(self, trade: TradeHistoryRow) -> Dict[str, Any]:
        """
        Prepare trade data for Firebase.

        Args:
            trade (TradeHistoryRow): Trade data to prepare

        Returns:
            Dict[str, Any]: Prepared trade data
        """
        # Helper function to safely convert to float
        def safe_float(value, default=0.0):
            return float(value) if value is not None else default

        trade_data = {
            "tradeID": trade.tradeID,
            "openTime": trade.openTime,
            "type": trade.type.value,
            "price": float(trade.price),
            "units": abs(float(trade.units)),
            "unrealizedPL": safe_float(trade.unrealizedPL),
            "status": trade.status.value,
            "initialMarginRequired": float(trade.initialMarginRequired),
            "instrument": trade.instrument
        }

        # Add closeTime if available
        if trade.closeTime:
            # Store as ISO format string
            self.logger.log_info(f"Setting closeTime for trade {trade.tradeID}: {trade.closeTime}")
            trade_data["closeTime"] = trade.closeTime.isoformat()

        # Add realizedPL if available
        trade_data["realizedPL"] = safe_float(trade.realizedPL)

        # Add halfSpreadCost if available
        if trade.halfSpreadCost is not None:
            trade_data["halfSpreadCost"] = safe_float(trade.halfSpreadCost)

        # Add commission if available
        if trade.commission is not None:
            trade_data["commission"] = safe_float(trade.commission)

        # Add take profit and stop loss prices if available
        if hasattr(trade, 'takeProfitPrice') and trade.takeProfitPrice is not None:
            trade_data["takeProfitPrice"] = float(trade.takeProfitPrice)

        if hasattr(trade, 'stopLossPrice') and trade.stopLossPrice is not None:
            trade_data["stopLossPrice"] = float(trade.stopLossPrice)

        return trade_data

    def _track_execution_time(self, operation: str, start_time: float) -> None:
        """
        Track execution time for an operation.

        Args:
            operation (str): Operation name
            start_time (float): Start time from time.time()
        """
        execution_time = time.time() - start_time

        if operation not in self.metrics['execution_times']:
            self.metrics['execution_times'][operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }

        metrics = self.metrics['execution_times'][operation]
        metrics['count'] += 1
        metrics['total_time'] += execution_time
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the Firebase client.

        Returns:
            Dict[str, Any]: Performance metrics
        """
        metrics = self.metrics.copy()

        # Calculate average execution times
        for operation, data in metrics['execution_times'].items():
            if data['count'] > 0:
                data['avg_time'] = data['total_time'] / data['count']

        return metrics

    # Removed batch_update method as it's not needed for FOREX trading
    # where only one trade per instrument is allowed

    def update_open_trades(self, open_trades: List[TradeHistoryRow]) -> None:
        """Update open trades in Firestore."""
        try:
            start_time = time.time()
            self.logger.log_info(f"Updating open trades for user {self.user_id}, strategy {self.strategy_id}")
            self.logger.log_info(f"Received open trades data: {open_trades}")

            # In FOREX, only one trade per instrument is allowed, so we don't need batch updates
            # Process each trade individually
            for trade in open_trades:
                trade_ref = (self.db.collection("users")
                             .document(self.user_id)
                             .collection("submittedStrategies")
                             .document(self.strategy_id)
                             .collection("tradeHistory").document(trade.tradeID))

                trade_data = self._prepare_trade_data(trade)
                self.metrics['operations'] += 1

                # For closed trades, we need to ensure halfSpreadCost and commission are set
                if trade.status == TradeStatus.CLOSED:
                    # Get the current trade history for given tradeID
                    current_trade_history = trade_ref.get()
                    if current_trade_history.exists:
                        current_trade_history_data = current_trade_history.to_dict()

                        # Helper function to safely convert to float
                        def safe_float(value, default=0.0):
                            return float(value) if value is not None else default

                        # Update halfSpreadCost if needed
                        if trade.halfSpreadCost is None and "halfSpreadCost" in current_trade_history_data:
                            trade_data["halfSpreadCost"] = safe_float(current_trade_history_data["halfSpreadCost"])
                        elif trade.halfSpreadCost is None:
                            trade_data["halfSpreadCost"] = 0.0

                        # Update commission if needed
                        if trade.commission is None and "commission" in current_trade_history_data:
                            trade_data["commission"] = safe_float(current_trade_history_data["commission"])
                        elif trade.commission is None:
                            trade_data["commission"] = 0.0
                    else:
                        self.logger.log_warning(f"No current trade history found for trade {trade.tradeID} when closing. Creating a new entry.")
                        # Set default values
                        if trade.halfSpreadCost is None:
                            trade_data["halfSpreadCost"] = 0.0
                        if trade.commission is None:
                            trade_data["commission"] = 0.0

                # Handle closeTime specially for Firestore timestamp conversion
                if trade.closeTime:
                    # Store as ISO format string
                    self.logger.log_info(f"Setting closeTime for trade {trade.tradeID}: {trade.closeTime}")
                    trade_data["closeTime"] = trade.closeTime.isoformat()

                trade_ref.set(trade_data)

        except Exception as e:
            self.logger.log_error(e, "Error updating open trades")
            raise
