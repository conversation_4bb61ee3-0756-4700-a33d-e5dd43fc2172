{"name": "Bollinger bands strategy", "description": "", "instruments": "EUR/USD", "timeframe": "1h", "tradingSession": ["All"], "indicators": [{"id": "1748033355743ym50rz7i1n", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": 2, "offset": 0}, "source": "price"}, {"id": "1748034933581vm6zvm0dm5g", "type": "ATR", "indicator_class": "ATR", "parameters": {"period": 14, "multiplier": 2}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "price", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1748033355743ym50rz7i1n", "value": "", "logicalOperator": "AND", "barRef": "close", "band2": "lower", "id": "1748034819052k7nsebwyo9m"}, {"id": "1748034837966kcsr2gqf5a", "tradeType": "long", "indicator1": "price", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1748033355743ym50rz7i1n", "value": "", "barRef": "close", "band2": "upper"}], "exitRules": [{"tradeType": "long", "indicator1": "price", "operator": ">", "compareType": "indicator", "indicator2": "1748033355743ym50rz7i1n", "value": "", "logicalOperator": "AND", "barRef": "close", "band2": "upper", "id": "17480349077358iwquh3a2oj"}, {"id": "1748034922808di8tx1zpdwm", "tradeType": "long", "indicator1": "price", "operator": "<", "compareType": "indicator", "indicator2": "1748033355743ym50rz7i1n", "value": "", "barRef": "close", "band2": "lower"}], "riskManagement": {"riskPercentage": "1", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "atr", "parameters": {"period": 14, "multiplier": 2}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "exitLongGroupOperator": "OR", "user_id": "Dhz64kmgEbh8lxA0OQZvVRx121DY", "id": "rVL2AmZDWIIC9PgwjZL3"}