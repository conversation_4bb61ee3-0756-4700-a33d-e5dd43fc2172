from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, cast
from datetime import datetime
import logging
import json
from utils.logger import Logger

class BaseStrategy(ABC):
    """Abstract base class for all trading strategies."""

    def __init__(self, strategy_json: str):
        """
        Initialize the strategy with configuration from JSON string.

        Args:
            strategy_json (str): Strategy configuration in JSON format
        """
        self.logger = Logger("BaseStrategy")

        try:
            # Parse JSON string to dictionary if it's a string
            if isinstance(strategy_json, str):
                try:
                    strategy_dict = json.loads(strategy_json)
                    self.logger.log_info("Successfully parsed strategy JSON string")
                except json.JSONDecodeError as e:
                    self.logger.log_error(e, f"Failed to parse strategy JSON: {strategy_json[:100]}...")
                    raise ValueError(f"Invalid JSON format: {str(e)}")
            else:
                # If it's already a dictionary, use it directly
                strategy_dict = strategy_json
                self.logger.log_info("Using provided strategy dictionary directly")

            # Log the strategy dict for debugging
            self.logger.log_info(f"Strategy dict before validation: {json.dumps(strategy_dict)}")

            # Check if risk management is in the nested format from the frontend
            if "riskManagement" in strategy_dict and isinstance(strategy_dict["riskManagement"], dict):
                risk_management = strategy_dict["riskManagement"]

                # Check if it has parameters and metrics structure
                if "parameters" in risk_management:
                    self.logger.log_info("Found nested risk management structure with parameters")

                    # Move stopLoss and takeProfit to the top level if they're in parameters
                    params = risk_management["parameters"]
                    if "stopLoss" in params and "stopLoss" not in risk_management:
                        risk_management["stopLoss"] = params["stopLoss"]
                    if "takeProfit" in params and "takeProfit" not in risk_management:
                        risk_management["takeProfit"] = params["takeProfit"]
                    if "stopLossUnit" in params and "stopLossUnit" not in risk_management:
                        risk_management["stopLossUnit"] = params["stopLossUnit"]
                    if "takeProfitUnit" in params and "takeProfitUnit" not in risk_management:
                        risk_management["takeProfitUnit"] = params["takeProfitUnit"]

                    self.logger.log_info(f"Updated risk management: {json.dumps(risk_management)}")

            # Validate the strategy configuration
            self._validate_strategy_dict(strategy_dict)

            # Initialize strategy attributes
            self.name = strategy_dict["name"]
            self.instrument = strategy_dict["instruments"]
            self.timeframe = strategy_dict["timeframe"]
            self.indicators = strategy_dict["indicators"]
            self.entry_rules = strategy_dict["entryRules"]
            self.exit_rules = strategy_dict["exitRules"]
            self.risk_management = strategy_dict["riskManagement"]
            # TimeZone is no longer used, default to UTC
            self.timezone = "UTC"

            # Get trading session if available
            self.trading_session = strategy_dict.get("tradingSession", [])

            # Get description if available
            self.description = strategy_dict.get("description", "")

            # Get group operators with defaults
            self.entry_long_group_operator = strategy_dict.get("entryLongGroupOperator", "OR")
            self.entry_short_group_operator = strategy_dict.get("entryShortGroupOperator", "OR")
            self.exit_long_group_operator = strategy_dict.get("exitLongGroupOperator", "OR")
            self.exit_short_group_operator = strategy_dict.get("exitShortGroupOperator", "OR")

            # For backward compatibility
            if "entryBuyGroupOperator" in strategy_dict:
                self.entry_long_group_operator = strategy_dict.get("entryBuyGroupOperator")
            if "exitBuyGroupOperator" in strategy_dict:
                self.exit_long_group_operator = strategy_dict.get("exitBuyGroupOperator")

            # Extract risk management parameters from the new structure
            self.risk_percentage = self.risk_management.get("riskPercentage")
            self.risk_reward_ratio = self.risk_management.get("riskRewardRatio")
            self.stop_loss_method = self.risk_management.get("stopLossMethod", "fixed")
            self.fixed_pips = self.risk_management.get("fixedPips") if self.stop_loss_method == "fixed" else None
            self.indicator_based_sl = self.risk_management.get("indicatorBasedSL") if self.stop_loss_method == "indicator" else None
            self.lot_size = self.risk_management.get("lotSize") if self.stop_loss_method == "risk" else None

            # Log the extracted values from new structure
            self.logger.log_info(f"Extracted risk percentage: {self.risk_percentage}")
            self.logger.log_info(f"Extracted risk reward ratio: {self.risk_reward_ratio}")
            self.logger.log_info(f"Extracted stop loss method: {self.stop_loss_method}")

            if self.stop_loss_method == "fixed":
                self.logger.log_info(f"Extracted fixed pips: {self.fixed_pips}")
            elif self.stop_loss_method == "indicator":
                self.logger.log_info(f"Extracted indicator based SL: {self.indicator_based_sl}")
            elif self.stop_loss_method == "risk":
                self.logger.log_info(f"Extracted lot size: {self.lot_size}")

            # Calculate stop loss and take profit values based on the method
            if self.stop_loss_method == "fixed":
                # SCENARIO 1: Fixed Pips
                self.stop_loss = str(self.fixed_pips) if self.fixed_pips is not None else "10"
                self.stop_loss_unit = "pips"

                # Calculate take profit based on risk reward ratio
                if self.risk_reward_ratio:
                    try:
                        stop_loss_value = float(self.stop_loss)
                        risk_reward_ratio = float(self.risk_reward_ratio)
                        take_profit_value = stop_loss_value * risk_reward_ratio
                        self.take_profit = str(take_profit_value)
                        self.logger.log_info(f"Calculated take profit from risk reward ratio: {self.take_profit} {self.stop_loss_unit}")
                    except (ValueError, TypeError) as e:
                        self.logger.log_warning(f"Failed to calculate take profit from risk reward ratio: {e}")
                        self.take_profit = "20"  # Default value
                else:
                    self.take_profit = "20"  # Default value

            elif self.stop_loss_method == "indicator":
                # SCENARIO 2: Indicator-Based SL
                if self.indicator_based_sl is not None:
                    try:
                        # Get ATR value and multiplier from indicator_based_sl
                        atr_value = self.indicator_based_sl.get("value", 0.001)  # Default to 0.001 if not provided
                        sl_multiplier = self.indicator_based_sl.get("multiplier", 1.5)  # Default to 1.5 if not provided

                        # Calculate SL in pips
                        sl_pips = atr_value * sl_multiplier
                        self.stop_loss = str(sl_pips)
                        self.stop_loss_unit = "pips"

                        # Calculate take profit based on risk reward ratio
                        if self.risk_reward_ratio:
                            risk_reward_ratio = float(self.risk_reward_ratio)
                            take_profit_value = sl_pips * risk_reward_ratio
                            self.take_profit = str(take_profit_value)
                            self.logger.log_info(f"Calculated take profit from indicator-based SL: {self.take_profit} {self.stop_loss_unit}")
                        else:
                            self.take_profit = str(sl_pips * 2)  # Default to 2x SL
                    except (ValueError, TypeError) as e:
                        self.logger.log_warning(f"Failed to calculate SL and TP from indicator: {e}")
                        self.stop_loss = "10"
                        self.stop_loss_unit = "pips"
                        self.take_profit = "20"
                else:
                    self.stop_loss = "10"
                    self.stop_loss_unit = "pips"
                    self.take_profit = "20"

            elif self.stop_loss_method == "risk":
                # SCENARIO 3: Risk-Based SL
                if self.lot_size is not None and self.risk_percentage is not None:
                    try:
                        # Calculate risk amount
                        risk_percentage = float(self.risk_percentage)
                        risk_amount = 10000 * (risk_percentage / 100.0)  # Using 10000 as a placeholder for account balance

                        # Calculate SL in pips based on lot size
                        lot_size = float(self.lot_size)

                        # Calculate pip value based on the instrument
                        # Use a default price of 1.0 if not available
                        pip_value = self._calculate_pip_value(self.instrument, 1.0)

                        # Calculate SL in pips
                        sl_pips = risk_amount / (lot_size * pip_value)
                        self.stop_loss = str(sl_pips)
                        self.stop_loss_unit = "pips"

                        # Calculate take profit based on risk reward ratio
                        if self.risk_reward_ratio:
                            risk_reward_ratio = float(self.risk_reward_ratio)
                            take_profit_value = sl_pips * risk_reward_ratio
                            self.take_profit = str(take_profit_value)
                            self.logger.log_info(f"Calculated take profit from risk-based SL: {self.take_profit} {self.stop_loss_unit}")
                        else:
                            self.take_profit = str(sl_pips * 2)  # Default to 2x SL
                    except (ValueError, TypeError, ZeroDivisionError) as e:
                        self.logger.log_warning(f"Failed to calculate SL and TP from lot size: {e}")
                        self.stop_loss = "10"
                        self.stop_loss_unit = "pips"
                        self.take_profit = "20"
                else:
                    self.stop_loss = "10"
                    self.stop_loss_unit = "pips"
                    self.take_profit = "20"
            else:
                # Default values if method is not recognized
                self.stop_loss = "2"
                self.stop_loss_unit = "percentage"

                # Calculate take profit based on risk reward ratio
                if self.risk_reward_ratio:
                    try:
                        stop_loss_value = float(self.stop_loss)
                        risk_reward_ratio = float(self.risk_reward_ratio)
                        take_profit_value = stop_loss_value * risk_reward_ratio
                        self.take_profit = str(take_profit_value)
                        self.logger.log_info(f"Calculated take profit from risk reward ratio: {self.take_profit}")
                    except (ValueError, TypeError) as e:
                        self.logger.log_warning(f"Failed to calculate take profit from risk reward ratio: {e}")
                        self.take_profit = "4"  # Default value
                else:
                    self.take_profit = "4"  # Default value

            # Use same unit for take profit as stop loss
            self.take_profit_unit = self.stop_loss_unit

            # Log the calculated values
            self.logger.log_info(f"Using stop loss: {self.stop_loss} {self.stop_loss_unit}")
            self.logger.log_info(f"Using take profit: {self.take_profit} {self.take_profit_unit}")

            # Set additional risk management parameters
            self.max_daily_loss = self.risk_management.get("maxDailyLoss", "5")
            self.max_position_size = self.risk_management.get("maxPositionSize", "10")
            self.runtime = self.risk_management.get("runtime", 7)
            self.total_profit_target = self.risk_management.get("totalProfitTarget", "20")
            self.total_loss_limit = self.risk_management.get("totalLossLimit", "10")
            self.avoid_high_spread = self.risk_management.get("avoidHighSpread", True)  # Default to True for safety

            # Log the extracted frontend risk management parameters
            if self.risk_percentage is not None:
                self.logger.log_info(f"Extracted risk percentage: {self.risk_percentage}%")
            if self.max_daily_loss is not None:
                self.logger.log_info(f"Extracted max daily loss: {self.max_daily_loss}%")
            if self.max_position_size is not None:
                self.logger.log_info(f"Extracted max position size: {self.max_position_size}%")
            if self.runtime is not None:
                self.logger.log_info(f"Extracted runtime: {self.runtime}")
            if self.total_profit_target is not None:
                self.logger.log_info(f"Extracted total profit target: {self.total_profit_target}%")
            if self.total_loss_limit is not None:
                self.logger.log_info(f"Extracted total loss limit: {self.total_loss_limit}%")
            self.logger.log_info(f"Avoid high spread: {self.avoid_high_spread}")

            # Format stop loss and take profit for backward compatibility
            if self.stop_loss_unit == "percentage" and not str(self.stop_loss).endswith("%"):
                self.stop_loss = f"{self.stop_loss}%"
            if self.take_profit_unit == "percentage" and not str(self.take_profit).endswith("%"):
                self.take_profit = f"{self.take_profit}%"

            # Initialize state
            self.current_state: Dict[str, Any] = {}

            # Strategy state
            self.is_running = False
            self.last_update = None
            self.current_position = None

            # Store strategy configuration
            self.config = strategy_dict

            self.logger.log_info(f"Strategy {self.name} initialized successfully")

        except json.JSONDecodeError as e:
            self.logger.log_error(e, "Failed to parse strategy JSON")
            raise ValueError(f"Invalid JSON format: {str(e)}")
        except Exception as e:
            self.logger.log_error(e, "Failed to initialize strategy")
            raise

    def _validate_strategy_dict(self, strategy_dict: Dict[str, Any]) -> None:
        """
        Validate that the strategy JSON contains all required fields.

        Args:
            strategy_json (Dict[str, Any]): Strategy configuration to validate

        Raises:
            ValueError: If any required fields are missing
        """
        # First, log the strategy dict for debugging
        self.logger.log_info(f"Validating strategy dict: {json.dumps(strategy_dict)}")

        required_fields = [
            "name",
            "instruments",
            "timeframe",
            "indicators",
            "entryRules",
            "exitRules",
            "riskManagement"
        ]

        missing_fields = []
        for field in required_fields:
            if field not in strategy_dict:
                missing_fields.append(field)

        if missing_fields:
            error_msg = f"Strategy JSON missing required fields: {', '.join(missing_fields)}"
            self.logger.log_error(ValueError(error_msg), "Strategy validation failed")
            raise ValueError(error_msg)

        # Validate risk management fields
        if "riskManagement" in strategy_dict:
            risk_management = strategy_dict["riskManagement"]
            self.logger.log_info(f"Risk management data: {json.dumps(risk_management)}")

            # Check for required stopLoss and takeProfit fields
            risk_fields = ["stopLoss", "takeProfit"]
            missing_risk_fields = []

            for field in risk_fields:
                if field not in risk_management:
                    missing_risk_fields.append(field)

            if missing_risk_fields:
                error_msg = f"Risk management missing required fields: {', '.join(missing_risk_fields)}"
                self.logger.log_error(ValueError(error_msg), "Strategy validation failed")
                raise ValueError(error_msg)

            # Log the found risk management fields
            self.logger.log_info(f"Found stopLoss: {risk_management.get('stopLoss')}")
            self.logger.log_info(f"Found takeProfit: {risk_management.get('takeProfit')}")

            # Check for additional risk management fields from the frontend
            frontend_risk_fields = ["riskPercentage", "maxDailyLoss", "maxPositionSize",
                                  "runtime", "totalProfitTarget", "totalLossLimit"]

            for field in frontend_risk_fields:
                if field in risk_management:
                    self.logger.log_info(f"Found frontend risk field: {field} = {risk_management.get(field)}")

        self.logger.log_info("Strategy JSON validated successfully")

    def update(self, market_data: Dict[str, Any], account_balance: float, open_trades: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Update strategy state and generate trading signals.

        Args:
            market_data (Dict[str, Any]): Latest market data
            account_balance (float): Current account balance
            open_trades (List[Dict[str, Any]], optional): List of open trades. Defaults to None.

        Returns:
            Dict[str, Any]: Strategy update including signals and indicators
        """
        self.last_update = datetime.utcnow()

        candles = market_data["candles"]
        indicators = market_data["indicators"]

        # Update strategy state
        self.update_state(candles, indicators)

        # Log open trades status
        if open_trades and len(open_trades) > 0:
            self.logger.log_info(f"Open trades detected: {open_trades}")
            self.logger.log_info("Checking for exit conditions first")
            # When we have open trades, we should prioritize checking exit conditions
        else:
            self.logger.log_info("No open trades, checking for entry conditions")

        # Check if we should trade based on trading session
        if hasattr(self, 'trading_session') and self.trading_session:
            if not self._is_in_trading_session():
                self.logger.log_info("Not in trading session, skipping signal generation")
                return {
                    "timestamp": self.last_update,
                    "indicators": indicators,
                    "signals": {"action": None, "reason": "Not in trading session"},
                    "current_position": self.current_position
                }

        # Calculate trading signals
        # If we have open trades, we should prioritize checking exit conditions
        if open_trades and len(open_trades) > 0:
            # Check exit conditions first
            self.logger.log_info(f"Checking exit conditions for open trades: {open_trades}")
            # Get the trade type (long/short) from the open trade
            trade_type = open_trades[0].type.value if hasattr(open_trades[0], 'type') else 'long'  # Default to long if not specified
            self.logger.log_info(f"Open trade type: {trade_type}")

            # Process exit rules based on trade type
            exit_signals = self._check_exit_conditions(market_data, trade_type)
            if exit_signals and exit_signals.get("action") == "CLOSE":
                self.logger.log_info(f"Exit signal generated: {exit_signals}")
                return {
                    "timestamp": self.last_update,
                    "indicators": indicators,
                    "signals": exit_signals,
                    "current_position": self.current_position
                }

        # If no exit signals or no open trades, check for entry signals
        self.logger.log_info(f"Calculating signals with exit rules: {self.exit_rules}")
        signals = self.calculate_signals(market_data=market_data)
        self.logger.log_info(f"Generated signals: {signals}")
        if signals is None:
            signals = {"action": None, "reason": "No signals generated"}

        # Calculate position size if entry signal
        if signals.get("action") == "BUY" or signals.get("action") == "buy":
            # Get the current price from the latest candle
            current_price = None
            if candles and len(candles) > 0:
                current_price = float(candles[-1]["close"])
                self.logger.log_info(f"Using current price for position sizing: {current_price}")

            signals["position_size"] = self.get_position_size(account_balance, current_price)

        return {
            "timestamp": self.last_update,
            "indicators": indicators,
            "signals": signals,
            "current_position": self.current_position
        }

    def calculate_indicators(self, market_data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        Calculate technical indicators based on market data.

        Args:
            market_data (List[Dict[str, Any]]): List of candle data

        Returns:
            Dict[str, List[float]]: Dictionary of indicator values
        """
        indicators: Dict[str, List[float]] = {}

        # First pass: Calculate indicators with price as source
        # Second pass: Calculate indicators with other indicators as source

        # Sort indicators by dependency (price-based first, then others)
        sorted_indicators = []
        indicators_with_dependencies = []

        for indicator in self.indicators:
            source = indicator.get("source", "price").lower()

            if source == "price":
                sorted_indicators.append(indicator)
            else:
                indicators_with_dependencies.append(indicator)

        # Add remaining indicators
        sorted_indicators.extend(indicators_with_dependencies)

        for indicator in sorted_indicators:
            indicator_class = indicator["indicator_class"]
            parameters = indicator["parameters"]
            source = indicator.get("source", "price").lower()
            indicator_id = indicator.get("id", indicator_class)

            # Store the indicator type for later use
            indicator_type = indicator_class

            try:
                # Get source data
                source_values = []

                if source == "price":
                    # Extract closing prices from candles by default
                    bar_ref = indicator.get("barRef", "close").lower()

                    if bar_ref == "close":
                        source_values = [float(candle["close"]) for candle in market_data]
                    elif bar_ref == "open":
                        source_values = [float(candle["open"]) for candle in market_data]
                    elif bar_ref == "high":
                        source_values = [float(candle["high"]) for candle in market_data]
                    elif bar_ref == "low":
                        source_values = [float(candle["low"]) for candle in market_data]
                    else:
                        # Default to close
                        source_values = [float(candle["close"]) for candle in market_data]
                else:
                    # Use another indicator as source
                    if source in indicators:
                        source_values = indicators[source]
                    else:
                        self.logger.log_warning(f"Source indicator {source} not found for {indicator_class}")
                        continue

                # Calculate indicator values based on class and parameters
                if indicator_class == "RSI":
                    values = self._calculate_rsi_from_values(source_values, parameters["period"])
                elif indicator_class == "SMA":
                    values = self._calculate_sma_from_values(source_values, parameters["period"])
                elif indicator_class == "EMA":
                    values = self._calculate_ema_from_values(source_values, parameters["period"])
                else:
                    self.logger.log_warning(f"Unsupported indicator class: {indicator_class}")
                    continue

                # Cast None values to 0.0 for type compatibility
                indicator_values = [0.0 if v is None else v for v in values]

                # Store the indicator values with both the ID and the type as keys
                indicators[indicator_id] = indicator_values

                # Also store with the indicator type as the key for easier frontend access
                # This ensures the frontend can access indicators by type (e.g., "RSI") regardless of the ID
                if indicator_type not in indicators:
                    indicators[indicator_type] = indicator_values

            except Exception as e:
                self.logger.log_error(e, f"Error calculating {indicator_class}")

        return indicators

    def get_position_size(self, account_balance: float, current_price: float = None, pip_value: float = None) -> float:
        """
        Calculate position size based on account balance and risk parameters.

        Args:
            account_balance (float): Current account balance
            current_price (float, optional): Current price of the instrument. Defaults to None.
            pip_value (float, optional): Value of one pip per unit. If None, calculated based on the instrument.

        Returns:
            float: Position size in units
        """
        # Ensure we have a valid current_price
        if current_price is None or current_price <= 0:
            self.logger.log_warning("No valid current price provided, using default value of 1.0")
            current_price = 1.0  # Use a default value if not provided

        # Calculate pip value if not provided
        if pip_value is None:
            pip_value = self._calculate_pip_value(self.instrument, current_price)
            self.logger.log_info(f"Calculated pip value for {self.instrument} at price {current_price}: {pip_value}")

        # Calculate risk amount based on risk percentage
        risk_percentage = 1.0  # Default to 1%
        if self.risk_percentage is not None:
            try:
                risk_percentage = float(self.risk_percentage)
                self.logger.log_info(f"Using risk percentage: {risk_percentage}%")
            except (ValueError, TypeError) as e:
                self.logger.log_warning(f"Failed to parse risk percentage: {e}")

        risk_amount = account_balance * (risk_percentage / 100.0)
        self.logger.log_info(f"Risk amount: {risk_amount}")

        # Calculate position size based on stop loss method
        if self.stop_loss_method == "fixed" and self.fixed_pips is not None:
            # SCENARIO 1: Fixed Pips
            try:
                sl_pips = float(self.fixed_pips)
                # Calculate lot size based on fixed pips
                lot_size = risk_amount / (sl_pips * pip_value)
                self.logger.log_info(f"Calculated lot size from fixed pips: {lot_size}")
                return lot_size
            except (ValueError, TypeError, ZeroDivisionError) as e:
                self.logger.log_warning(f"Failed to calculate lot size from fixed pips: {e}")

        elif self.stop_loss_method == "indicator" and self.indicator_based_sl is not None:
            # SCENARIO 2: Indicator-Based SL
            try:
                # Get ATR value and multiplier from indicator_based_sl
                atr_value = self.indicator_based_sl.get("value", 0.001)  # Default to 0.001 if not provided
                sl_multiplier = self.indicator_based_sl.get("multiplier", 1.5)  # Default to 1.5 if not provided

                # Calculate SL in pips
                sl_pips = atr_value * sl_multiplier

                # Calculate lot size
                lot_size = risk_amount / (sl_pips * pip_value)
                self.logger.log_info(f"Calculated lot size from indicator-based SL: {lot_size}")
                return lot_size
            except (ValueError, TypeError, ZeroDivisionError) as e:
                self.logger.log_warning(f"Failed to calculate lot size from indicator-based SL: {e}")

        elif self.stop_loss_method == "risk" and self.lot_size is not None:
            # SCENARIO 3: Risk-Based SL
            try:
                # Use the user-specified lot size
                lot_size = float(self.lot_size)
                self.logger.log_info(f"Using user-specified lot size: {lot_size}")
                return lot_size
            except (ValueError, TypeError) as e:
                self.logger.log_warning(f"Failed to parse lot size: {e}")

        # Default to risk percentage of account balance if no specific method is used
        default_lot_size = account_balance * (risk_percentage / 100.0)
        self.logger.log_info(f"Using default lot size calculation: {default_lot_size}")
        return default_lot_size

    def _calculate_pip_value(self, instrument: str, current_price: float = None) -> float:
        """
        Calculate the pip value for a given currency pair.

        Args:
            instrument (str): The currency pair (e.g., "EUR_USD" or "EUR/USD")
            current_price (float, optional): The current exchange rate. Defaults to None.

        Returns:
            float: The pip value per unit of the base currency
        """
        # Default pip value (for 4-digit forex pairs)
        default_pip_value = 0.0001

        # If no instrument is provided, return default
        if not instrument:
            self.logger.log_warning(f"Using default pip value {default_pip_value} due to missing instrument")
            return default_pip_value

        # If no price is provided, return default
        if not current_price or current_price <= 0:
            self.logger.log_warning(f"Using default pip value {default_pip_value} due to invalid price: {current_price}")
            return default_pip_value

        try:
            # Normalize the instrument format (replace / or _ with nothing)
            normalized_instrument = instrument.replace('_', '').replace('/', '')
            self.logger.log_info(f"Normalized instrument: {normalized_instrument} from {instrument}")

            # Determine if it's a JPY pair (5-digit precision vs 4-digit)
            is_jpy_pair = 'JPY' in normalized_instrument
            pip_size = 0.01 if is_jpy_pair else 0.0001

            # Extract base and quote currencies
            if len(normalized_instrument) == 6:
                base_currency = normalized_instrument[:3]
                quote_currency = normalized_instrument[3:]
                self.logger.log_info(f"Extracted base currency: {base_currency}, quote currency: {quote_currency}")
            else:
                self.logger.log_warning(f"Unexpected instrument format: {instrument} (normalized: {normalized_instrument}), using default pip value")
                return default_pip_value

            # Calculate pip value based on the pair type
            if quote_currency == 'USD':  # e.g., EUR/USD, GBP/USD
                # For USD quote pairs, pip value is fixed at 10 USD per standard lot (100,000 units)
                # So per unit it's 0.0001 USD
                self.logger.log_info(f"USD quote pair detected, using pip size: {pip_size}")
                return pip_size
            elif base_currency == 'USD':  # e.g., USD/JPY, USD/CAD
                # For USD base pairs, pip value = pip_size / exchange rate per unit
                pip_value = pip_size / current_price
                self.logger.log_info(f"USD base pair detected, calculated pip value: {pip_value} using price: {current_price}")
                return pip_value
            else:  # Cross pairs like EUR/GBP
                # For cross pairs, we need USD conversion rates, but we'll use an approximation
                # This is a simplification; in a real system, you'd look up USD rates
                self.logger.log_info(f"Cross pair detected, using approximated pip size: {pip_size}")
                return pip_size  # Simplified approximation

        except Exception as e:
            self.logger.log_warning(f"Error calculating pip value: {e}, using default")
            return default_pip_value

    def _calculate_rsi(self, market_data: List[Dict[str, Any]], period: int) -> List[Union[float, None]]:
        """
        Calculate Relative Strength Index (RSI) from market data.

        Args:
            market_data (List[Dict[str, Any]]): List of candle data
            period (int): RSI period

        Returns:
            List[Union[float, None]]: RSI values
        """
        # Get closing prices
        closes = [float(candle["close"]) for candle in market_data]
        return self._calculate_rsi_from_values(closes, period)

    def _calculate_rsi_from_values(self, values: List[float], period: int) -> List[Union[float, None]]:
        """
        Calculate Relative Strength Index (RSI) from a list of values.

        Args:
            values (List[float]): List of price values
            period (int): RSI period

        Returns:
            List[Union[float, None]]: RSI values
        """
        if len(values) < period + 1:
            return [None] * len(values)

        # Calculate price changes
        changes = [values[i] - values[i-1] for i in range(1, len(values))]

        # Initialize gains and losses
        gains = [max(0, change) for change in changes]
        losses = [abs(min(0, change)) for change in changes]

        # Calculate average gains and losses
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period

        # Calculate RSI values
        rsi_values = []
        for i in range(period, len(values)):
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))

            rsi_values.append(rsi)

            # Update averages
            avg_gain = (avg_gain * (period - 1) + gains[i-1]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i-1]) / period

        # Pad with None for periods before RSI can be calculated
        return cast(List[Union[float, None]], [None] * period + rsi_values)

    def _calculate_sma(self, market_data: List[Dict[str, Any]], period: int) -> List[Union[float, None]]:
        """
        Calculate Simple Moving Average (SMA) from market data.

        Args:
            market_data (List[Dict[str, Any]]): List of candle data
            period (int): SMA period

        Returns:
            List[Union[float, None]]: SMA values
        """
        # Get closing prices
        closes = [float(candle["close"]) for candle in market_data]
        return self._calculate_sma_from_values(closes, period)

    def _calculate_sma_from_values(self, values: List[float], period: int) -> List[Union[float, None]]:
        """
        Calculate Simple Moving Average (SMA) from a list of values.
        This function can be used with any source data, including:
        - Price data (open, high, low, close)
        - Volume data
        - Other indicator values

        Args:
            values (List[float]): List of values (can be prices, volumes, or other indicator values)
            period (int): SMA period

        Returns:
            List[Union[float, None]]: SMA values with None for periods before SMA can be calculated
        """
        sma_values = []

        for i in range(len(values)):
            if i < period - 1:
                sma_values.append(None)
            else:
                # Calculate SMA for the current window
                window = values[i-period+1:i+1]
                # Filter out None values if any
                valid_values = [v for v in window if v is not None]
                if len(valid_values) >= period * 0.8:  # At least 80% of values should be valid
                    sma = sum(valid_values) / len(valid_values)
                    sma_values.append(sma)
                else:
                    sma_values.append(None)

        return cast(List[Union[float, None]], sma_values)

    def _calculate_ema(self, market_data: List[Dict[str, Any]], period: int) -> List[Union[float, None]]:
        """
        Calculate Exponential Moving Average (EMA) from market data.

        Args:
            market_data (List[Dict[str, Any]]): List of candle data
            period (int): EMA period

        Returns:
            List[Union[float, None]]: EMA values
        """
        # Get closing prices
        closes = [float(candle["close"]) for candle in market_data]
        return self._calculate_ema_from_values(closes, period)

    def _calculate_ema_from_values(self, values: List[float], period: int) -> List[Union[float, None]]:
        """
        Calculate Exponential Moving Average (EMA) from a list of values.
        This function can be used with any source data, including:
        - Price data (open, high, low, close)
        - Volume data
        - Other indicator values

        Args:
            values (List[float]): List of values (can be prices, volumes, or other indicator values)
            period (int): EMA period

        Returns:
            List[Union[float, None]]: EMA values with None for periods before EMA can be calculated
        """
        if len(values) < period:
            return [None] * len(values)

        multiplier = 2 / (period + 1)
        ema_values = []

        # Filter out None values for initial SMA calculation
        valid_initial_values = [v for v in values[:period] if v is not None]
        if len(valid_initial_values) >= period * 0.8:  # At least 80% of values should be valid
            # Start with SMA for first period
            sma = sum(valid_initial_values) / len(valid_initial_values)
            ema_values.extend([None] * (period - 1))
            ema_values.append(sma)

            # Calculate EMA values
            for i in range(period, len(values)):
                if values[i] is not None and ema_values[-1] is not None:
                    ema = (values[i] - ema_values[-1]) * multiplier + ema_values[-1]
                    ema_values.append(ema)
                else:
                    # If current value or previous EMA is None, append None
                    ema_values.append(None)
        else:
            # Not enough valid values for initial SMA
            return [None] * len(values)

        return cast(List[Union[float, None]], ema_values)

    def validate_config(self) -> bool:
        """
        Validate the strategy configuration.

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        required_fields = ["name", "instruments", "timeframe"]
        return all(field in self.config for field in required_fields)

    def update_state(self, candles: List[Dict], indicators: Dict[str, List[float]]):
        """
        Update the strategy's internal state.

        Args:
            candles (List[Dict]): List of candle data
            indicators (Dict[str, List[float]]): Dictionary of indicator values
        """
        self.current_state = {
            "candles": candles,
            "indicators": indicators,
            "timestamp": datetime.utcnow().isoformat()
        }

    def _is_in_trading_session(self) -> bool:
        """
        Check if the current time is within the specified trading session.

        Returns:
            bool: True if in trading session, False otherwise
        """
        # If no trading session is specified, allow trading at any time
        if not hasattr(self, 'trading_session') or not self.trading_session:
            return True

        # If "All" is in the trading sessions, allow trading at any time
        if "All" in self.trading_session:
            return True

        # Get current UTC time
        current_time = datetime.utcnow()
        current_hour = current_time.hour

        # Check if current time is in any of the specified trading sessions
        for session in self.trading_session:
            if session == "New York":
                # New York session: 8:00-17:00 EST (13:00-22:00 UTC)
                if 13 <= current_hour < 22:
                    return True
            elif session == "London":
                # London session: 8:00-17:00 GMT (8:00-17:00 UTC)
                if 8 <= current_hour < 17:
                    return True
            elif session == "Tokyo":
                # Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
                if 0 <= current_hour < 9:
                    return True
            elif session == "Sydney":
                # Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
                if current_hour >= 22 or current_hour < 7:
                    return True

        # Not in any trading session
        return False

    def calculate_signals(self, market_data: Dict[str, Any]) -> Optional[Dict]:
        """
        Calculate trading signals based on current state.

        Args:
            market_data (Dict[str, Any]): Market data including candles and indicators

        Returns:
            Optional[Dict]: Trading signals if any, None otherwise
        """
        if not self.current_state:
            return None

        # Separate entry rules by trade type
        entry_long_rules = [rule for rule in self.entry_rules if rule.get("tradeType", "").lower() == "long"]
        entry_short_rules = [rule for rule in self.entry_rules if rule.get("tradeType", "").lower() == "short"]

        # For backward compatibility
        if not entry_long_rules and not entry_short_rules:
            entry_long_rules = [rule for rule in self.entry_rules if rule.get("tradeType", "").lower() == "buy"]
            entry_short_rules = [rule for rule in self.entry_rules if rule.get("tradeType", "").lower() == "sell"]

        # Check entry long rules
        entry_long_results = []
        for rule in entry_long_rules:
            rule_result = self._check_rule(rule)
            entry_long_results.append(rule_result)

        # Handle entry long rules based on group operator
        if entry_long_results:
            should_enter_long = False
            if self.entry_long_group_operator.upper() == "AND":
                should_enter_long = all(entry_long_results)
            else:  # OR is the default
                should_enter_long = any(entry_long_results)

            if should_enter_long:
                triggered_rule = entry_long_rules[0]
                if any(entry_long_results):
                    triggered_rule = entry_long_rules[entry_long_results.index(True)]

                return {
                    "action": "BUY",  # Using BUY for backward compatibility
                    "reason": f"Entry LONG rule triggered: {triggered_rule['id']}",
                    "rule_id": triggered_rule["id"]
                }

        # Check entry short rules
        entry_short_results = []
        for rule in entry_short_rules:
            rule_result = self._check_rule(rule)
            entry_short_results.append(rule_result)

        # Handle entry short rules based on group operator
        if entry_short_results:
            should_enter_short = False
            if self.entry_short_group_operator.upper() == "AND":
                should_enter_short = all(entry_short_results)
            else:  # OR is the default
                should_enter_short = any(entry_short_results)

            if should_enter_short:
                triggered_rule = entry_short_rules[0]
                if any(entry_short_results):
                    triggered_rule = entry_short_rules[entry_short_results.index(True)]

                return {
                    "action": "SELL",  # Using SELL for backward compatibility
                    "reason": f"Entry SHORT rule triggered: {triggered_rule['id']}",
                    "rule_id": triggered_rule["id"]
                }

        # Separate exit rules by trade type
        self.logger.log_info(f"Processing exit rules: {self.exit_rules}")
        exit_long_rules = [rule for rule in self.exit_rules if rule.get("tradeType", "").lower() == "long"]
        exit_short_rules = [rule for rule in self.exit_rules if rule.get("tradeType", "").lower() == "short"]
        self.logger.log_info(f"Exit long rules: {exit_long_rules}")
        self.logger.log_info(f"Exit short rules: {exit_short_rules}")

        # For backward compatibility
        if not exit_long_rules and not exit_short_rules:
            self.logger.log_info("No exit rules found with 'long'/'short', trying 'buy'/'sell' for backward compatibility")
            exit_long_rules = [rule for rule in self.exit_rules if rule.get("tradeType", "").lower() == "buy"]
            exit_short_rules = [rule for rule in self.exit_rules if rule.get("tradeType", "").lower() == "sell"]
            self.logger.log_info(f"Exit long rules after compatibility check: {exit_long_rules}")
            self.logger.log_info(f"Exit short rules after compatibility check: {exit_short_rules}")

        # Check exit long rules
        exit_long_results = []
        for rule in exit_long_rules:
            self.logger.log_info(f"Checking exit long rule: {rule}")
            rule_result = self._check_rule(rule)
            self.logger.log_info(f"Exit long rule result: {rule_result}")
            exit_long_results.append(rule_result)

        # Handle exit long rules based on group operator
        if exit_long_results:
            should_exit_long = False
            if self.exit_long_group_operator.upper() == "AND":
                should_exit_long = all(exit_long_results)
            else:  # OR is the default
                should_exit_long = any(exit_long_results)

            if should_exit_long:
                triggered_rule = exit_long_rules[0]
                if any(exit_long_results):
                    triggered_rule = exit_long_rules[exit_long_results.index(True)]

                return {
                    "action": "CLOSE",
                    "reason": f"Exit LONG rule triggered: {triggered_rule['id']}",
                    "rule_id": triggered_rule["id"]
                }

        # Check exit short rules
        exit_short_results = []
        for rule in exit_short_rules:
            rule_result = self._check_rule(rule)
            exit_short_results.append(rule_result)

        # Handle exit short rules based on group operator
        if exit_short_results:
            should_exit_short = False
            if self.exit_short_group_operator.upper() == "AND":
                should_exit_short = all(exit_short_results)
            else:  # OR is the default
                should_exit_short = any(exit_short_results)

            if should_exit_short:
                triggered_rule = exit_short_rules[0]
                if any(exit_short_results):
                    triggered_rule = exit_short_rules[exit_short_results.index(True)]

                return {
                    "action": "CLOSE",
                    "reason": f"Exit SHORT rule triggered: {triggered_rule['id']}",
                    "rule_id": triggered_rule["id"]
                }

        return {"action": None, "reason": "No signals generated"}

    def _check_rule(self, rule: Dict) -> bool:
        """
        Check if a trading rule is satisfied.

        Args:
            rule (Dict): Trading rule to check

        Returns:
            bool: True if rule is satisfied, False otherwise
        """
        # Get the first indicator values
        current_value = None
        previous_value = None

        # Special handling for 'price' as an indicator
        if rule["indicator1"].lower() == "price":
            # Use the price from the candles
            if "candles" not in self.current_state or len(self.current_state["candles"]) < 2:
                self.logger.log_warning("Not enough candles to check price rule")
                return False

            # Get the bar reference (close, open, high, low)
            bar_ref = rule.get("barRef", "close").lower()

            # Get the current and previous values based on bar reference
            if bar_ref == "close":
                current_value = self.current_state["candles"][-1]["close"]
                previous_value = self.current_state["candles"][-2]["close"]
            elif bar_ref == "open":
                current_value = self.current_state["candles"][-1]["open"]
                previous_value = self.current_state["candles"][-2]["open"]
            elif bar_ref == "high":
                current_value = self.current_state["candles"][-1]["high"]
                previous_value = self.current_state["candles"][-2]["high"]
            elif bar_ref == "low":
                current_value = self.current_state["candles"][-1]["low"]
                previous_value = self.current_state["candles"][-2]["low"]
            else:
                # Default to close
                current_value = self.current_state["candles"][-1]["close"]
                previous_value = self.current_state["candles"][-2]["close"]
        else:
            # Regular indicator handling
            indicator1 = next((ind for ind in self.indicators if ind["id"] == rule["indicator1"]), None)
            if not indicator1:
                self.logger.log_warning(f"Indicator with ID {rule['indicator1']} not found")
                return False

            indicator1_name = indicator1["indicator_class"]
            self.logger.log_info(f"Looking for indicator {indicator1_name} with ID {rule['indicator1']}")
            self.logger.log_info(f"Available indicators in current state: {list(self.current_state['indicators'].keys())}")

            if indicator1_name not in self.current_state["indicators"]:
                # Try using the ID as a fallback
                if rule["indicator1"] in self.current_state["indicators"]:
                    self.logger.log_info(f"Found indicator using ID {rule['indicator1']} instead of class name {indicator1_name}")
                    indicator1_name = rule["indicator1"]
                else:
                    self.logger.log_warning(f"Indicator {indicator1_name} not found in current state")
                    return False

            indicator1_values = self.current_state["indicators"][indicator1_name]
            if not indicator1_values or len(indicator1_values) < 2:
                self.logger.log_warning(f"Not enough values for indicator {indicator1_name}")
                return False

            current_value = indicator1_values[-1]
            previous_value = indicator1_values[-2]
            self.logger.log_info(f"Indicator {indicator1_name} values - current: {current_value}, previous: {previous_value}")

        # Handle different comparison types
        if rule["compareType"] == "value":
            try:
                compare_value = float(rule["value"])
                self.logger.log_info(f"Comparing values - current: {current_value}, previous: {previous_value}, compare: {compare_value}, operator: {rule['operator']}")
                result = self._compare_values(current_value, previous_value, compare_value, rule["operator"])
                self.logger.log_info(f"Comparison result: {result}")
                return result
            except (ValueError, TypeError):
                self.logger.log_warning(f"Invalid value for comparison: {rule['value']}")
                return False
        elif rule["compareType"] == "indicator":
            # Special handling for 'price' as the second indicator
            if rule["indicator2"].lower() == "price":
                # Use the price from the candles
                if "candles" not in self.current_state or not self.current_state["candles"]:
                    self.logger.log_warning("No candles to check price rule")
                    return False

                # Get the bar reference (close, open, high, low)
                bar_ref = rule.get("barRef", "close").lower()

                # Get the compare value based on bar reference
                if bar_ref == "close":
                    compare_value = self.current_state["candles"][-1]["close"]
                elif bar_ref == "open":
                    compare_value = self.current_state["candles"][-1]["open"]
                elif bar_ref == "high":
                    compare_value = self.current_state["candles"][-1]["high"]
                elif bar_ref == "low":
                    compare_value = self.current_state["candles"][-1]["low"]
                else:
                    # Default to close
                    compare_value = self.current_state["candles"][-1]["close"]
            else:
                # Regular indicator handling
                indicator2 = next((ind for ind in self.indicators if ind["id"] == rule["indicator2"]), None)
                if not indicator2:
                    self.logger.log_warning(f"Second indicator with ID {rule['indicator2']} not found")
                    return False

                indicator2_name = indicator2["indicator_class"]
                if indicator2_name not in self.current_state["indicators"]:
                    self.logger.log_warning(f"Second indicator {indicator2_name} not found in current state")
                    return False

                indicator2_values = self.current_state["indicators"][indicator2_name]
                if not indicator2_values:
                    return False

                compare_value = indicator2_values[-1]

            return self._compare_values(current_value, previous_value, compare_value, rule["operator"])

        return False

    def _check_exit_conditions(self, market_data: Dict[str, Any], trade_type: str) -> Optional[Dict[str, Any]]:
        """
        Check if any exit conditions are met for the current open trade.

        Args:
            market_data (Dict[str, Any]): Market data including candles and indicators
            trade_type (str): Type of the open trade ('long' or 'short')

        Returns:
            Optional[Dict[str, Any]]: Exit signal if conditions are met, None otherwise
        """
        self.logger.log_info(f"Checking exit conditions for trade type: {trade_type}")

        # Get exit rules for the current trade type
        exit_rules = [rule for rule in self.exit_rules if rule.get("tradeType", "").lower() == trade_type.lower()]
        self.logger.log_info(f"Found {len(exit_rules)} exit rules for trade type {trade_type}: {exit_rules}")

        if not exit_rules:
            self.logger.log_info(f"No exit rules found for trade type: {trade_type}")
            return None

        # Check each exit rule
        for rule in exit_rules:
            self.logger.log_info(f"Checking exit rule: {rule}")

            # Get indicator values
            indicator1 = next((ind for ind in self.indicators if ind["id"] == rule["indicator1"]), None)
            if not indicator1:
                self.logger.log_warning(f"Indicator with ID {rule['indicator1']} not found")
                continue

            indicator1_name = indicator1["indicator_class"]
            self.logger.log_info(f"Looking for indicator {indicator1_name} with ID {rule['indicator1']}")
            self.logger.log_info(f"Available indicators in current state: {list(self.current_state['indicators'].keys())}")

            # Try to get indicator values by class name or ID
            indicator_values = None
            if indicator1_name in self.current_state["indicators"]:
                indicator_values = self.current_state["indicators"][indicator1_name]
            elif rule["indicator1"] in self.current_state["indicators"]:
                indicator_values = self.current_state["indicators"][rule["indicator1"]]

            if not indicator_values or len(indicator_values) < 2:
                self.logger.log_warning(f"Not enough values for indicator {indicator1_name}")
                continue

            current_value = indicator_values[-1]
            previous_value = indicator_values[-2]
            self.logger.log_info(f"Indicator {indicator1_name} values - current: {current_value}, previous: {previous_value}")

            # Compare values based on rule
            if rule["compareType"] == "value":
                try:
                    compare_value = float(rule["value"])
                    self.logger.log_info(f"Comparing values - current: {current_value}, previous: {previous_value}, compare: {compare_value}, operator: {rule['operator']}")
                    result = self._compare_values(current_value, previous_value, compare_value, rule["operator"])
                    self.logger.log_info(f"Comparison result: {result}")

                    if result:
                        self.logger.log_info(f"Exit condition met for rule: {rule}")
                        return {
                            "action": "CLOSE",
                            "reason": f"Exit {trade_type.upper()} rule triggered: {rule['id']}",
                            "rule_id": rule["id"]
                        }
                except (ValueError, TypeError) as e:
                    self.logger.log_warning(f"Invalid value for comparison: {rule['value']} - {str(e)}")
                    continue

        self.logger.log_info(f"No exit conditions met for trade type: {trade_type}")
        return None

    def _compare_values(self, current: float, previous: float, compare_value: float, operator: str) -> bool:
        """
        Compare values based on the specified operator.

        Args:
            current (float): Current value
            previous (float): Previous value
            compare_value (float): Value to compare against
            operator (str): Comparison operator

        Returns:
            bool: True if comparison is satisfied, False otherwise
        """
        if operator == "Crossing above":
            return previous <= compare_value and current > compare_value
        elif operator == "Crossing below":
            return previous >= compare_value and current < compare_value
        elif operator == ">":
            return current > compare_value
        elif operator == "<":
            return current < compare_value
        elif operator == "==":
            return abs(current - compare_value) < 0.0001  # Using small epsilon for float comparison
        else:
            self.logger.log_warning(f"Unsupported operator: {operator}")
            return False

    def get_timeframe(self) -> str:
        """
        Get the timeframe of the strategy.
        """
        return self.timeframe

    def generate_human_readable_rules(self) -> Dict[str, Any]:
        """
        Generate human-readable representation of entry and exit rules in a frontend-friendly format.

        Returns:
            Dict[str, Any]: Dictionary containing human-readable rules
        """
        def format_indicator(indicator_id: str) -> Dict[str, Any]:
            """Format an indicator with its parameters."""
            indicator = next((ind for ind in self.indicators if ind["id"] == indicator_id), None)
            if not indicator:
                return {
                    "name": "Unknown Indicator",
                    "parameters": {}
                }

            return {
                "name": indicator["indicator_class"],
                "parameters": indicator["parameters"]
            }

        def format_rule(rule: Dict) -> Dict[str, Any]:
            """Format a single rule into a frontend-friendly structure."""
            indicator1 = format_indicator(rule["indicator1"])

            if rule["compareType"] == "value":
                comparison = {
                    "type": "value",
                    "operator": rule["operator"],
                    "value": rule["value"]
                }
            else:
                indicator2 = format_indicator(rule["indicator2"]) if rule.get("indicator2") else None
                comparison = {
                    "type": "indicator",
                    "operator": rule["operator"],
                    "indicator": indicator2
                }

            return {
                "id": rule["id"],
                "trade_type": rule["tradeType"],
                "indicator1": indicator1,
                "comparison": comparison
            }

        def format_rule_pair(entry_rule: Dict, exit_rule: Dict) -> Dict[str, Any]:
            """Format an entry/exit rule pair."""
            return {
                "entry": format_rule(entry_rule),
                "exit": format_rule(exit_rule)
            }

        # Pair entry and exit rules based on trade type
        rule_pairs = []
        for entry_rule in self.entry_rules:
            # Find matching exit rule with the same trade type
            exit_rule = next(
                (rule for rule in self.exit_rules if rule["tradeType"] == entry_rule["tradeType"]),
                None
            )
            if exit_rule:
                rule_pairs.append(format_rule_pair(entry_rule, exit_rule))

        return {
            "human_readable_rules": {
                "strategy_info": {
                    "name": self.name,
                    "instrument": self.instrument,
                    "timeframe": self.timeframe
                },
                "rule_pairs": rule_pairs,
                "risk_management": {
                    "stop_loss": self.risk_management.get("stopLoss", "1%"),
                    "take_profit": self.risk_management.get("takeProfit", "2%")
                }
            }
        }
