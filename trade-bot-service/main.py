import os
import time
import signal
import sys
import json
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta, timezone
import requests
from google.cloud import pubsub_v1
import google.api_core.exceptions
import threading
from core.config_manager import ConfigManager
from core.trading_engine import TradingEngine
from core.fast_update_loop import FastUpdate<PERSON>oop
from data.market_data import MarketDataProvider
from execution.oanda_client import OandaClient
from utils.logger import Logger
from utils.error_messages import get_user_friendly_error_message
from execution.firebase_client import FirebaseClient
from strategies.base_strategy import BaseStrategy
from utils.health_client import HealthClient
from utils.recovery_manager import RecoveryManager
from utils.retry_utils import OrderTracker

# Contacted Polygon support and they recommended a buffer of 1 second
# to ensure the new candle data is available.
POLYGON_BUFFER_SECONDS = 1

class TradingBot:
    """Main trading bot service."""

    def __init__(self, user_id: str, strategy_id: str):
        """Initialize trading bot."""
        self.logger = Logger("TradingBot")
        self.user_id = user_id
        self.strategy_id = strategy_id
        self.market_data_provider: MarketDataProvider
        self.strategy: BaseStrategy
        self.trading_engine: TradingEngine
        self.oanda_client: OandaClient
        self.firebase_client: FirebaseClient
        self.fast_update_loop: FastUpdateLoop = None
        self.strategy_controller_url: str = os.getenv("STRATEGY_CONTROLLER_URL", "http://strategy-controller:8080")
        self.is_paused: bool = False
        self.should_exit: bool = False  # Flag to indicate if the bot should exit
        self.subscriber = pubsub_v1.SubscriberClient()
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
        self.command_topic_name = os.getenv('COMMAND_TOPIC_NAME', 'strategy-commands')
        self.command_subscription_name = f"strategy-{self.strategy_id}-commands"
        self.command_subscription_path = self.subscriber.subscription_path(
            self.project_id, self.command_subscription_name
        )
        self.running = True
        self.command_thread = None

        # Initialize health client with the correct URL
        # If running locally, use localhost instead of strategy-controller
        controller_url = self.strategy_controller_url
        if "strategy-controller" in controller_url and not os.getenv("KUBERNETES_SERVICE_HOST"):
            controller_url = controller_url.replace("strategy-controller", "localhost")
            self.logger.log_info(f"Using modified URL for health client: {controller_url}")

        self.health_client = HealthClient(self.strategy_id, self.user_id, controller_url)

        # Initialize recovery manager
        self.recovery_manager = RecoveryManager(self.strategy_id, logger=self.logger)

        # Initialize order tracker
        self.order_tracker = OrderTracker(logger=self.logger)

        # Create a file to indicate if the bot has been explicitly stopped
        self.stop_flag_file = f"/tmp/trade_bot_stopped_{self.strategy_id}"
        # Check if the stop flag file exists and remove it on startup
        if os.path.exists(self.stop_flag_file):
            try:
                os.remove(self.stop_flag_file)
                self.logger.log_info(f"Removed existing stop flag file: {self.stop_flag_file}")
            except Exception as e:
                self.logger.log_warning(f"Could not remove stop flag file: {str(e)}")

        try:
            # Get user_id from environment
            user_id = os.getenv("USER_ID")
            if not user_id:
                raise ValueError("USER_ID environment variable not set")
            self.user_id = str(user_id)
            self.logger.log_info(f"User ID found: {self.user_id}")

            # Get strategy_id from environment
            strategy_id = os.getenv("STRATEGY_ID")
            if not strategy_id:
                raise ValueError("STRATEGY_ID environment variable not set")
            self.strategy_id = str(strategy_id)
            self.logger.log_info(f"Strategy ID found: {self.strategy_id}")

            # Initialize Firebase client first
            self.firebase_client = FirebaseClient(user_id=self.user_id, strategy_id=self.strategy_id)

            polygon_api_key = os.getenv("POLYGON_API_KEY")
            if not polygon_api_key:
                raise ValueError("POLYGON_API_KEY environment variable not set")

            # Update bot status in Firebase
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.INITIALIZING,
                {"message": "Trading bot initializing..."}
            )

            self.market_data_provider = MarketDataProvider(api_key=str(polygon_api_key))
            self.logger.log_info("Market data provider initialized")

            # Initialize OANDA client
            self.oanda_client = OandaClient(self.firebase_client, health_client=self.health_client)
            self.logger.log_info("OANDA client initialized")

            # Load strategy from Firestore
            strategy_doc = self.firebase_client.db.collection('users').document(self.user_id)\
                .collection('submittedStrategies').document(self.strategy_id).get()
            if not strategy_doc.exists:
                raise ValueError(f"Strategy {self.strategy_id} not found for user {self.user_id}")
            strategy_json = strategy_doc.get('strategy_json')
            if not strategy_json:
                raise ValueError(f"Strategy JSON not found for strategy {self.strategy_id}")
            self.logger.log_info(f"Strategy fetched from Firestore: {strategy_json}")

            # Initialize market data provider
            self.market_data_provider = MarketDataProvider(api_key=str(polygon_api_key))
            self.logger.log_info("Market data provider initialized")

            # Initialize strategy
            self.strategy = BaseStrategy(strategy_json)
            self.logger.log_info("Strategy initialized")

            # Generate and save human-readable rules
            human_readable_rules = self.strategy.generate_human_readable_rules()
            self.firebase_client.update_human_readable_rules(
                human_readable_rules
            )
            self.logger.log_info("Human-readable rules saved to strategy document")

            # Initialize trading engine
            self.trading_engine = TradingEngine(
                strategy=self.strategy,
                market_data_provider=self.market_data_provider,
                oanda_client=self.oanda_client,
                user_id=self.user_id,
                strategy_id=self.strategy_id,
                firebase_client=self.firebase_client,
                health_client=self.health_client
            )
            self.logger.log_info("Trading engine initialized")

            # Initialize fast update loop
            self.fast_update_loop = FastUpdateLoop(
                trading_engine=self.trading_engine,
                oanda_client=self.oanda_client,
                firebase_client=self.firebase_client,
                update_interval=5  # Update every 5 seconds
            )
            self.logger.log_info("Fast update loop initialized")

            # Setup signal handlers
            self.logger.log_info("Setting up signal handlers...")
            signal.signal(signal.SIGINT, self._handle_shutdown)
            signal.signal(signal.SIGTERM, self._handle_shutdown)
            self.logger.log_info("Signal handlers configured")

            # Update bot status in Firebase
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.INITIALIZED,
                {"message": "Trading bot initialized successfully"}
            )

            # Inform the user that the trading bot has been initialized successfully
            self.firebase_client.append_user_log(
                "☀️ Trading bot initialized successfully",
            )

            self.logger.log_info("Trading bot initialized successfully")

        except Exception as e:
            self.logger.log_error(e, "Failed to initialize trading bot")
            raise

    def _handle_shutdown(self, signum: int, frame):
        """
        Handle shutdown signals with graceful cleanup.

        Args:
            signum (int): Signal number
            frame: Current stack frame
        """
        signal_name = "SIGINT" if signum == signal.SIGINT else "SIGTERM" if signum == signal.SIGTERM else f"Signal {signum}"

        try:
            self.logger.log_info(f"Received shutdown signal: {signal_name}")

            # Temporarily ignore additional signals during cleanup
            # This prevents multiple cleanup attempts if user presses Ctrl+C multiple times
            signal.signal(signal.SIGINT, signal.SIG_IGN)
            signal.signal(signal.SIGTERM, signal.SIG_IGN)

            # Perform cleanup with a timeout
            self.cleanup(timeout_seconds=60)  # Allow up to 60 seconds for cleanup during signal handling

            self.logger.log_info(f"Graceful shutdown completed after {signal_name}")
        except Exception as e:
            self.logger.log_error(e, f"Error during shutdown from {signal_name}")
        finally:
            # Exit with appropriate code (0 for SIGTERM, 130 for SIGINT which is standard for Ctrl+C)
            exit_code = 130 if signum == signal.SIGINT else 0
            sys.exit(exit_code)

    def _get_next_timeframe_interval(self, timeframe: str) -> datetime:
        """
        Calculate the next interval based on the timeframe.

        Args:
            timeframe (str): Timeframe string (e.g., "1h", "4h", "1d")

        Returns:
            datetime: Next interval datetime
        """
        now = datetime.now(timezone.utc)

        # Extract multiplier and unit from timeframe
        multiplier = int(''.join(filter(str.isdigit, timeframe))) if any(c.isdigit() for c in timeframe) else 1
        unit = ''.join([i for i in timeframe if not i.isdigit()]).lower()

        # Map timeframe units to timedelta parameters
        unit_map = {
            'm': 'minutes',
            'h': 'hours',
            'd': 'days',
            'w': 'weeks'
        }

        if unit not in unit_map:
            self.logger.log_error(f"Unsupported timeframe unit: {unit}")
            return now + timedelta(minutes=1)  # Default to 1 minute

        # Calculate the next interval
        delta_param = {unit_map[unit]: multiplier}
        current_interval = now.replace(microsecond=0)

        # Round down to the current interval
        if unit == 'm':
            current_interval = current_interval.replace(second=0)
        elif unit == 'h':
            current_interval = current_interval.replace(minute=0, second=0)
        elif unit == 'd':
            current_interval = current_interval.replace(hour=0, minute=0, second=0)
        elif unit == 'w':
            current_interval = current_interval.replace(hour=0, minute=0, second=0)
            # Round down to the start of the week (Monday)
            current_interval = current_interval - timedelta(days=current_interval.weekday())

        # Add one interval to get the next one
        next_interval = current_interval + timedelta(**delta_param)

        # If we're already past the current interval, use the next one
        if now >= next_interval:
            next_interval = next_interval + timedelta(**delta_param)

        # Add a buffer time based on the timeframe to ensure data is available
        # This is to ensure Polygon has a couple of seconds to update the new candle data
        return next_interval + timedelta(seconds=POLYGON_BUFFER_SECONDS)

    def start_command_listener(self):
        """Start listening for commands in a background thread."""
        def listen_for_commands():
            while self.running:
                try:
                    # Create subscription if it doesn't exist
                    try:
                        self.subscriber.create_subscription(
                            request={
                                "name": self.command_subscription_path,
                                "topic": f"projects/{self.project_id}/topics/{self.command_topic_name}",
                                "filter": f'attributes.strategy_id="{self.strategy_id}"'
                            }
                        )
                    except Exception as e:
                        if "already exists" not in str(e):
                            self.logger.log_error(e, "Failed to create command subscription")
                            time.sleep(5)
                            continue

                    # Pull messages
                    response = self.subscriber.pull(
                        request={
                            "subscription": self.command_subscription_path,
                            "max_messages": 1
                        }
                    )

                    if not response.received_messages:
                        time.sleep(1)
                        continue

                    for received_message in response.received_messages:
                        try:
                            # Process the message
                            message_data = json.loads(received_message.message.data.decode("utf-8"))

                            if message_data.get("user_id") != self.user_id:
                                self.logger.log_warning(f"Received command for different user: {message_data.get('user_id')}")
                                continue

                            command = message_data.get("command")
                            parameters = message_data.get("parameters", {})

                            if command == "pause":
                                self.handle_pause_command()
                            elif command == "resume":
                                self.handle_resume_command()
                            elif command == "stop":
                                self.logger.log_info("Received stop command")
                                self.should_exit = True

                                # Create a stop flag file to prevent the bot from restarting
                                try:
                                    with open(self.stop_flag_file, 'w') as f:
                                        f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()}")
                                    self.logger.log_info(f"Created stop flag file: {self.stop_flag_file}")
                                except Exception as e:
                                    self.logger.log_error(f"Could not create stop flag file: {str(e)}")

                                self.cleanup()
                                sys.exit(0)

                            # Acknowledge the message
                            self.subscriber.acknowledge(
                                request={
                                    "subscription": self.command_subscription_path,
                                    "ack_ids": [received_message.ack_id]
                                }
                            )

                        except Exception as e:
                            self.logger.log_error(e, "Error processing command message")

                except Exception as e:
                    self.logger.log_error(e, "Error in command listener")
                    time.sleep(5)

        self.command_thread = threading.Thread(target=listen_for_commands)
        self.command_thread.daemon = True
        self.command_thread.start()
        self.logger.log_info("Command listener started")

    def handle_pause_command(self):
        """
        Handle the pause command.
        When the bot is paused, we want to do the following:
        - Close all open trades
        - Update the bot status in Firebase
        - Keep the fast update loop running for monitoring
        """
        self.logger.log_info("Received pause command")
        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.PAUSING,
            {"message": "Trading bot pausing..."}
        )
        self.firebase_client.append_user_log(
            "⏰ Trading bot pausing..."
        )
        # Set paused flag first to prevent any new trades during closing process
        self.is_paused = True
        # Close all open trades if any
        self.trading_engine.close_and_update_open_trades()
        self.firebase_client.append_user_log(
            "✅ All open trades closed"
        )
        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.PAUSED,
            {"message": "Trading bot paused"}
        )
        self.firebase_client.append_user_log(
            "⏸️ Trading bot paused successfully!"
        )

        # Note: We keep the fast update loop running even when paused
        # This allows us to continue monitoring market conditions and updating UI

    def handle_resume_command(self):
        """
        Handle the resume command.
        When the bot is resumed, we want to do the following:
        - Update the bot status in Firebase
        - Ensure the fast update loop is running
        """
        self.logger.log_info("Received resume command")
        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.RESUMING,
            {"message": "Trading bot resuming..."}
        )
        self.is_paused = False

        # Ensure the fast update loop is running
        if self.fast_update_loop and not self.fast_update_loop.running:
            self.fast_update_loop.start()
            self.logger.log_info("Fast update loop restarted")

    def run(self):
        """Run the trading bot."""
        try:
            # Check if this strategy is in the stopped strategies set
            try:
                # Make a request to the strategy controller to check if this strategy is stopped
                response = requests.get(
                    f"{self.strategy_controller_url}/check-strategy-status/{self.user_id}/{self.strategy_id}",
                    timeout=5
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get("is_stopped", False):
                        self.logger.log_info(f"Strategy {self.strategy_id} was explicitly stopped and will not be restarted")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.STOPPED,
                            {"message": "Trading bot was explicitly stopped and will not be restarted"}
                        )
                        self.firebase_client.append_user_log(
                            "⏹️ Trading bot was explicitly stopped and will not be restarted"
                        )
                        return
            except Exception as e:
                # If we can't check the status, just continue
                self.logger.log_warning(f"Could not check if strategy is stopped: {str(e)}")

            # Start the health client
            self.health_client.start(interval=60)

            # Start the command listener
            self.start_command_listener()

            # Start the fast update loop
            if self.fast_update_loop:
                self.fast_update_loop.start()
                self.logger.log_info("Fast update loop started")

            # Check for pending operations to recover
            self._recover_pending_operations()

            # Set the bot status to running
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.RUNNING,
                {"message": "Trading bot is now running"}
            )

            self.logger.log_info("Trading bot initialized successfully and is now running")

            # Add user log
            self.firebase_client.append_user_log(
                "🚀 Trading bot is now running and analyzing the market."
            )

            # Get the timeframe from the strategy
            timeframe = self.trading_engine.strategy.get_timeframe()
            self.logger.log_info(f"Using timeframe: {timeframe}")

            # Flag to track if this is the first update cycle (to skip waiting for the next timeframe)
            first_cycle = True

            while True:
                try:
                    # Check if the bot should exit
                    if self.should_exit or os.path.exists(self.stop_flag_file):
                        self.logger.log_info("Bot has been explicitly stopped, exiting main loop...")
                        break

                    if self.is_paused:
                        self.logger.log_info("Bot is paused, waiting...")
                        time.sleep(5)
                        continue

                    # Start immediately for the first update, then sync with timeframe intervals
                    wait_seconds = 0
                    if not first_cycle:
                        # We need the trading engine loop to be in sync with real world time
                        # This ensures that the loop always runs right after the previous candle (depending on the timeframe)
                        # closes. Therefore allowing it to immediately react to the previous candle.
                        # Calculate next interval
                        next_interval = self._get_next_timeframe_interval(timeframe)
                        wait_seconds = (next_interval - datetime.now(timezone.utc)).total_seconds()

                    if wait_seconds > 0:
                        self.logger.log_info(f"Waiting {wait_seconds:.2f} seconds until next interval at {next_interval}")

                        # Wait in smaller increments so we can check for exit conditions
                        wait_increment = 5  # Check every 5 seconds
                        remaining_wait = wait_seconds

                        while remaining_wait > 0:
                            # Check if the bot should exit
                            if self.should_exit or os.path.exists(self.stop_flag_file):
                                self.logger.log_info("Bot has been explicitly stopped during wait, exiting main loop...")
                                return

                            # Sleep for the smaller increment or the remaining time
                            sleep_time = min(wait_increment, remaining_wait)
                            time.sleep(sleep_time)
                            remaining_wait -= sleep_time

                    # Check paused state again after waiting
                    if self.is_paused:
                        self.logger.log_info("Bot is paused after waiting interval, skipping update cycle...")
                        continue

                    # Check exit condition again
                    if self.should_exit or os.path.exists(self.stop_flag_file):
                        self.logger.log_info("Bot has been explicitly stopped after waiting, exiting main loop...")
                        break

                    # Update trading engine state
                    self.logger.log_info("Analyzing market conditions and updating trading state...")

                    try:
                        result = self.trading_engine.update()

                        # Set first_cycle to False after the first cycle
                        if first_cycle:
                            first_cycle = False
                            self.logger.log_info("Initial market analysis complete, continuing with regular updates")
                    except SystemExit as e:
                        self.logger.log_info(f"Received SystemExit: {str(e)}")
                        self.firebase_client.append_user_log(
                            "⚠️ Trading bot stopped due to critical error. Please check the logs for details."
                        )
                        # Set the should_exit flag to ensure we exit the main loop
                        self.should_exit = True
                        # Create a stop flag file if it doesn't exist
                        if not os.path.exists(self.stop_flag_file):
                            try:
                                with open(self.stop_flag_file, 'w') as f:
                                    f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()} due to: {str(e)}")
                                self.logger.log_info(f"Created stop flag file: {self.stop_flag_file}")
                            except Exception as ex:
                                self.logger.log_error(f"Could not create stop flag file: {str(ex)}")
                        # Break out of the main loop
                        break

                    if result["status"] == "error":
                        self.logger.log_error(Exception(result["message"]), "Trading engine update failed")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.ERROR,
                            {"message": result["message"]}
                        )
                    elif result["status"] == "market_closed":
                        self.logger.log_info("Market is closed, waiting...")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.MARKET_CLOSED,
                            {"message": "Market is closed"}
                        )
                    elif result["status"] == "not_in_session":
                        self.logger.log_info(f"Not in trading session, waiting... {result.get('message', '')}")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.NOT_IN_SESSION,
                            {"message": result.get('message', 'Outside trading session')}
                        )
                    elif result["status"] == "market_data_error":
                        # Log the technical error details for debugging
                        technical_error = result.get("technical_details", "Unknown error")
                        self.logger.log_error(Exception(technical_error), "Market data error")

                        # Use the user-friendly message for the UI
                        user_message = result["message"]

                        # Update bot status with user-friendly message
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.ERROR,
                            {"message": user_message}
                        )

                        # Add user log with friendly message
                        self.firebase_client.append_user_log(
                            f"❌ Market data issue: {user_message}. Stopping bot."
                        )
                        break
                    else:
                        self.logger.log_info("Update cycle completed successfully")
                        # Only update status to RUNNING if not paused
                        if not self.is_paused:
                            self.firebase_client.update_bot_status(
                                FirebaseClient.BotStatus.RUNNING,
                                {"message": "Trading bot running normally"}
                            )
                        else:
                            # If paused, make sure status reflects this
                            self.firebase_client.update_bot_status(
                                FirebaseClient.BotStatus.PAUSED,
                                {"message": "Trading bot paused"}
                            )

                        # Update chart data if available
                        if "market_data" in result:
                            self.firebase_client.update_chart_data(
                                result["market_data"]["candles"],
                                result["market_data"]["indicators"]
                            )
                        # Update trade history if a trade was executed
                        if "trade" in result and result["trade"] is not None:
                            trade_history_row = self.trading_engine.convert_trade_execution_response_to_trade_history_row(result["trade"])

                            self.firebase_client.add_to_trade_history(
                                trade_history_row
                            )
                        else:
                            self.logger.log_info("No trade executed")
                            if "open_trades" in result:
                                self.firebase_client.update_open_trades(
                                    result["open_trades"]
                                )

                        # Update account balance if available
                        if "account_summary" in result:
                            if self.firebase_client:
                                self.firebase_client.update_account_balance(
                                    result["account_summary"]["balance"]
                                )

                except Exception as e:
                    self.logger.log_error(e, "Error in trading loop")

                    # Create a user-friendly error message
                    user_message = get_user_friendly_error_message(str(e))

                    self.firebase_client.update_bot_status(
                        FirebaseClient.BotStatus.ERROR,
                        {"message": user_message}
                    )

                    # Add user log with friendly message
                    self.firebase_client.append_user_log(
                        f"⚠️ Trading bot encountered an issue: {user_message}. Attempting to recover..."
                    )

                    # Wait for a short time before retrying
                    time.sleep(5)

        except Exception as e:
            self.logger.log_error(e, "Fatal error in trading bot")

            # Create a user-friendly error message
            user_message = get_user_friendly_error_message(str(e))

            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.ERROR,
                {"message": user_message}
            )

            # Add user log with friendly message
            self.firebase_client.append_user_log(
                f"⛔ Trading bot stopped due to an error: {user_message}. Please restart the bot."
            )
        finally:
            self.cleanup()
            pass

    def _recover_pending_operations(self):
        """Recover any pending operations from previous runs."""
        try:
            # Define handlers for different operation types
            handlers = {
                "trade_execution": self._recover_trade_execution,
                "position_close": self._recover_position_close,
                "risk_update": self._recover_risk_update
            }

            # Recover pending operations
            recovered_count = self.recovery_manager.recover_pending_operations(handlers)

            if recovered_count > 0:
                self.logger.log_info(f"Recovered {recovered_count} pending operations")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered {recovered_count} pending operations from previous session"
                )

            # Load last checkpoint if available
            checkpoint = self.recovery_manager.get_last_checkpoint()
            if checkpoint:
                self.logger.log_info(f"Found checkpoint {checkpoint['checkpoint_id']} from {checkpoint['last_updated']}")
                # TODO: Implement checkpoint recovery logic if needed
        except Exception as e:
            self.logger.log_error(e, "Error recovering pending operations")

    def _recover_trade_execution(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a trade execution operation."""
        try:
            trade_type = params.get("trade_type")
            instrument = params.get("instrument")
            units = params.get("units")
            order_id = params.get("order_id")

            self.logger.log_info(f"Recovering trade execution: {trade_type} {units} units of {instrument}")

            # Check if the order was already executed
            if order_id:
                # Check with the broker if the order exists
                try:
                    order_status = self.oanda_client.get_order(order_id)
                    self.logger.log_info(f"Order {order_id} already exists with status {order_status['state']}")
                    return {"status": "already_executed", "order_id": order_id, "order_status": order_status}
                except Exception as e:
                    self.logger.log_warning(f"Order {order_id} not found, will re-execute: {str(e)}")

            # Re-execute the trade
            result = self.trading_engine.execute_trade(trade_type, instrument, units)

            self.logger.log_info(f"Re-executed trade: {result}")
            self.firebase_client.append_user_log(
                f"🔄 Recovered interrupted trade: {trade_type} {units} units of {instrument}"
            )

            return result
        except Exception as e:
            self.logger.log_error(e, f"Error recovering trade execution: {params}")
            raise

    def _recover_position_close(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a position close operation."""
        try:
            instrument = params.get("instrument")
            trade_id = params.get("trade_id")

            self.logger.log_info(f"Recovering position close for {instrument} (trade ID: {trade_id})")

            # Check if the position is still open
            open_trades = self.oanda_client.get_open_trades()

            # If trade_id is provided, check if that specific trade is still open
            if trade_id:
                trade_still_open = any(trade["id"] == trade_id for trade in open_trades)
                if not trade_still_open:
                    self.logger.log_info(f"Trade {trade_id} is already closed")
                    return {"status": "already_closed", "trade_id": trade_id}

                # Close the specific trade
                result = self.trading_engine.close_trade(trade_id)

                self.logger.log_info(f"Closed trade {trade_id}: {result}")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered interrupted position close for trade {trade_id}"
                )

                return result
            else:
                # Close all trades for the instrument if no specific trade_id
                instrument_trades = [trade for trade in open_trades if trade["instrument"] == instrument]

                if not instrument_trades:
                    self.logger.log_info(f"No open trades for {instrument}")
                    return {"status": "no_open_trades", "instrument": instrument}

                results = []
                for trade in instrument_trades:
                    result = self.trading_engine.close_trade(trade["id"])
                    results.append(result)

                self.logger.log_info(f"Closed {len(results)} trades for {instrument}")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered interrupted position close for {len(results)} trades of {instrument}"
                )

                return {"status": "success", "results": results}
        except Exception as e:
            self.logger.log_error(e, f"Error recovering position close: {params}")
            raise

    def _recover_risk_update(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a risk update operation."""
        try:
            risk_params = params.get("risk_params", {})

            self.logger.log_info(f"Recovering risk update: {risk_params}")

            # Update risk parameters
            self.trading_engine.update_risk_parameters(risk_params)

            self.logger.log_info("Updated risk parameters")
            self.firebase_client.append_user_log(
                "🔄 Recovered interrupted risk parameter update"
            )

            return {"status": "success", "risk_params": risk_params}
        except Exception as e:
            self.logger.log_error(e, f"Error recovering risk update: {params}")
            raise

    def cleanup(self, timeout_seconds: int = 30):
        """
        Clean up resources and close positions with timeout protection.

        This function is called:
        - When the trading bot is stopped
        - When the trading bot is stopped due to an error
        - When the trading bot receives a SIGINT or SIGTERM signal

        It closes all open positions and updates the bot status in Firebase.

        Args:
            timeout_seconds (int): Maximum time to spend on cleanup before giving up
        """
        # Set a flag to prevent multiple cleanup calls
        if hasattr(self, '_cleanup_in_progress') and self._cleanup_in_progress:
            self.logger.log_info("Cleanup already in progress, skipping duplicate call")
            return

        self._cleanup_in_progress = True
        self.running = False

        # Start timing the cleanup process
        start_time = time.time()

        # Log the start of cleanup
        self.logger.log_info(f"Starting cleanup process with {timeout_seconds}s timeout")

        try:
            # Step 1: Gracefully stop the command listener thread
            self._cleanup_command_thread(timeout_seconds=5)

            # Check if we've exceeded our timeout
            if time.time() - start_time > timeout_seconds:
                self.logger.log_warning("Cleanup timeout exceeded after command thread shutdown")
                self._finalize_cleanup(success=False, error_message="Cleanup timed out")
                return

            # Step 2: Update status to stopping
            try:
                self.logger.log_info("Updating bot status to STOPPING...")
                self.firebase_client.update_bot_status(
                    FirebaseClient.BotStatus.STOPPING,
                    {"message": "Trading bot stopping..."}
                )
                self.firebase_client.append_user_log(
                    "⏰ Trading bot stopping..."
                )
            except Exception as firebase_error:
                # Don't let Firebase errors stop the cleanup process
                self.logger.log_error(firebase_error, "Error updating bot status to STOPPING")

            # Step 3: Close all open trades
            if self.trading_engine:
                # Calculate remaining time for trade closing
                remaining_time = timeout_seconds - (time.time() - start_time)
                if remaining_time <= 0:
                    self.logger.log_warning("No time remaining for trade closing")
                else:
                    self.logger.log_info(f"Closing open trades with {remaining_time:.1f}s remaining")
                    try:
                        # Use the is_shutdown flag to ensure we continue even if some trades fail to close
                        trades_closed = self.trading_engine.close_and_update_open_trades(is_shutdown=True)
                        if not trades_closed:
                            self.logger.log_warning("Some trades could not be closed during shutdown")
                    except Exception as trade_error:
                        self.logger.log_error(trade_error, "Error closing trades during shutdown")

            # Step 4: Final status update
            self._finalize_cleanup(success=True)

            # Step 5: Clean up subscription
            try:
                # Check if the subscription exists before trying to delete it
                try:
                    self.subscriber.get_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    # If we get here, the subscription exists and we can delete it
                    self.subscriber.delete_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    self.logger.log_info("Command subscription deleted")
                except google.api_core.exceptions.NotFound:
                    # Subscription doesn't exist, which is fine
                    self.logger.log_info("Command subscription already deleted or doesn't exist")
                except Exception as get_error:
                    # If we can't check if the subscription exists, try to delete it anyway
                    self.logger.log_warning(f"Error checking if subscription exists: {str(get_error)}")
                    self.subscriber.delete_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    self.logger.log_info("Command subscription deleted")
            except Exception as sub_error:
                # This is not a critical error, so just log it
                self.logger.log_warning(f"Error deleting command subscription: {str(sub_error)}")

            # Step 6: Stop the health client
            try:
                if hasattr(self, 'health_client'):
                    self.logger.log_info("Stopping health client")
                    self.health_client.update_status("stopped")
                    self.health_client.send_health_data()  # Send final health update
                    self.health_client.stop()
                    self.logger.log_info("Health client stopped successfully")
            except Exception as health_error:
                self.logger.log_error(health_error, "Error stopping health client")

            # Step 7: Stop the fast update loop
            try:
                if hasattr(self, 'fast_update_loop') and self.fast_update_loop:
                    self.logger.log_info("Stopping fast update loop")
                    self.fast_update_loop.stop()
                    self.logger.log_info("Fast update loop stopped successfully")
            except Exception as fast_update_error:
                self.logger.log_error(fast_update_error, "Error stopping fast update loop")

        except Exception as e:
            self.logger.log_error(e, "Error during cleanup")
            self._finalize_cleanup(success=False, error_message=str(e))
        finally:
            # Always clear the cleanup flag
            self._cleanup_in_progress = False

    def _cleanup_command_thread(self, timeout_seconds: int = 5):
        """
        Gracefully stop the command listener thread.

        Args:
            timeout_seconds (int): Maximum time to wait for the thread to stop
        """
        try:
            # Check if we're not in the command thread before trying to join it
            current_thread = threading.current_thread()
            if self.command_thread is not None and current_thread != self.command_thread:
                self.logger.log_info(f"Waiting up to {timeout_seconds}s for command thread to stop...")
                self.command_thread.join(timeout=timeout_seconds)
                if self.command_thread.is_alive():
                    self.logger.log_warning("Command thread did not stop within the timeout period")
                else:
                    self.logger.log_info("Command thread stopped successfully")
        except Exception as e:
            self.logger.log_error(e, "Error stopping command thread")

    def _finalize_cleanup(self, success: bool, error_message: str = None):
        """
        Finalize the cleanup process by updating the bot status.

        Args:
            success (bool): Whether the cleanup was successful
            error_message (str, optional): Error message if cleanup failed
        """
        try:
            # Create a stop flag file to prevent the bot from restarting
            # This is a backup in case the flag wasn't created during the stop command
            if not os.path.exists(self.stop_flag_file):
                try:
                    with open(self.stop_flag_file, 'w') as f:
                        f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()}")
                    self.logger.log_info(f"Created stop flag file during cleanup: {self.stop_flag_file}")
                except Exception as e:
                    self.logger.log_error(f"Could not create stop flag file during cleanup: {str(e)}")

            # Set the should_exit flag
            self.should_exit = True

            if success:
                self.firebase_client.update_bot_status(
                    FirebaseClient.BotStatus.STOPPED,
                    {"message": "Trading bot stopped"}
                )
                self.firebase_client.append_user_log(
                    "⏹️ Trading bot stopped successfully!"
                )
                self.logger.log_info("Cleanup completed successfully")
            else:
                # Create a user-friendly error message
                user_message = get_user_friendly_error_message(error_message or "Unknown error during shutdown")

                self.firebase_client.update_bot_status(
                    FirebaseClient.BotStatus.ERROR,
                    {"message": user_message}
                )
                self.firebase_client.append_user_log(
                    f"⚠️ Trading bot stopped with some issues: {user_message}"
                )
                self.logger.log_warning(f"Cleanup completed with errors: {error_message}")
        except Exception as e:
            # Last resort logging if even the status update fails
            self.logger.log_error(e, "Error during final cleanup status update")

def main():
    """Main entry point for the trading bot service."""
    # Check required environment variables
    required_vars = [
        "POLYGON_API_KEY",
        "USER_ID",  # This will be set by the Strategy Controller
        "STRATEGY_ID"  # This will be set by the Strategy Controller
    ]

    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
        sys.exit(1)

    # Create and start trading bot
    bot = TradingBot(os.getenv("USER_ID"), os.getenv("STRATEGY_ID"))
    bot.run()

if __name__ == "__main__":
    main()