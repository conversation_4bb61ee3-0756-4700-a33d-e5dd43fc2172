#!/bin/bash
# Run the strategy controller with local trade bot mode enabled

# Set environment variables for local development
export USE_LOCAL_TRADE_BOT=true
export BYPASS_MARKET_IS_CLOSED=true
export FIRESTORE_EMULATOR_HOST=127.0.0.1:8082
export PUBSUB_EMULATOR_HOST=localhost:8085
export GOOGLE_CLOUD_PROJECT=oryntrade
export OANDA_PRACTICE_MODE=true

# Check for polygon API key in ../.env or trade-bot-service/.env
if [ -f "../.env" ]; then
  echo "Loading environment from ../.env"
  source ../.env
fi

if [ -f "../trade-bot-service/.env" ]; then
  echo "Loading environment from ../trade-bot-service/.env"
  source ../trade-bot-service/.env
fi

# Set up virtual environment
if [ -d "venv" ]; then
  echo "Activating existing virtual environment..."
  source venv/bin/activate
else
  echo "Creating new virtual environment..."
  python -m venv venv
  source venv/bin/activate
  
  echo "Installing dependencies..."
  if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
  else
    # Install common dependencies needed for the service
    pip install fastapi uvicorn firebase-admin google-cloud-pubsub kubernetes python-dotenv
  fi
fi

# Install trade bot dependencies if needed
echo "Checking and installing trade bot dependencies..."
pip install numpy pandas matplotlib ta scikit-learn

# Ensure emulators are running
echo "Please ensure Firebase emulators are running (Firestore on port 8082, PubSub on port 8085)"
echo "You can start them with: cd functions && npm run serve"

# Run the strategy controller
echo "Starting strategy controller with local trade bot mode..."
python main.py 