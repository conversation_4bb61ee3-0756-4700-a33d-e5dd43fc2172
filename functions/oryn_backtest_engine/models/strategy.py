from dataclasses import dataclass
from typing import List, Dict, Union, Optional
from enum import Enum
from datetime import datetime, time

class TradeType(str, Enum):
    LONG = "long"
    SHORT = "short"

class CompareType(str, Enum):
    INDICATOR = "indicator"
    VALUE = "value"

class Operator(str, Enum):
    CROSSING_ABOVE = "Crossing above"
    CROSSING_BELOW = "Crossing below"
    GREATER_THAN = ">"
    LESS_THAN = "<"
    EQUALS = "=="
    GREATER_EQUAL = ">="
    LESS_EQUAL = "<="

class TradingSession(str, Enum):
    ALL = "All"
    NEW_YORK = "New York"
    LONDON = "London"
    TOKYO = "Tokyo"
    SYDNEY = "Sydney"

# Trading session times in UTC
SESSION_TIMES = {
    TradingSession.NEW_YORK: (time(13, 30), time(22, 0)),   # 8:30 AM - 5:00 PM EST
    TradingSession.LONDON: (time(8, 0), time(16, 30)),      # 8:00 AM - 4:30 PM GMT
    TradingSession.TOKYO: (time(0, 0), time(8, 30)),        # 9:00 AM - 5:30 PM JST
    TradingSession.SYDNEY: (time(22, 0), time(7, 0))        # 8:00 AM - 5:00 PM AEST
}

@dataclass
class IndicatorConfig:
    id: str
    type: str
    parameters: Dict[str, Union[int, float, str]]
    source: str  # Can be 'price', 'volume', or another indicator's ID

@dataclass
class RuleCondition:
    trade_type: TradeType
    indicator1: str  # Indicator ID or 'price'
    operator: Operator
    compare_type: CompareType
    indicator2: Optional[str] = None  # Required if compare_type is INDICATOR
    value: Optional[Union[float, str]] = None  # Required if compare_type is VALUE
    logical_operator: str = "AND"
    bar_ref: str = ""  # Required if indicator1 is 'price'
    band: Optional[str] = None  # For Bollinger Bands - which band to use for indicator1 (upper, middle, lower)
    band2: Optional[str] = None  # For Bollinger Bands - which band to use for indicator2 (upper, middle, lower)

class StopLossMethod(str, Enum):
    FIXED = "fixed"
    RISK = "risk"
    INDICATOR = "indicator"

class IndicatorType(str, Enum):
    ATR = "atr"
    BOLLINGER_BANDS = "bollinger"
    SUPPORT_RESISTANCE = "support_resistance"

@dataclass
class IndicatorBasedSL:
    indicator: str  # atr, bollinger, support_resistance
    parameters: Dict[str, Union[int, float, str]]

@dataclass
class RiskManagement:
    # Old fields (for backward compatibility)
    stop_loss: Optional[str] = None  # Format: "1%" or "100 pips"
    take_profit: Optional[str] = None  # Format: "2%" or "200 pips"

    # New fields
    risk_percentage: Optional[str] = "1"  # Default 1% of account per trade
    risk_reward_ratio: Optional[str] = "2"  # Default 2:1 risk-reward ratio
    stop_loss_method: Optional[StopLossMethod] = StopLossMethod.FIXED
    fixed_pips: Optional[str] = None  # For fixed pips stop loss
    lot_size: Optional[str] = None  # For risk-based stop loss
    indicator_based_sl: Optional[IndicatorBasedSL] = None  # For indicator-based stop loss

@dataclass
class TradingCosts:
    """Trading costs configuration."""
    spread_pips: float = 1.0  # Default 1 pip spread
    commission_percentage: float = 0.0  # Default no commission
    commission_fixed: float = 0.0  # Default no fixed commission per trade

@dataclass
class Strategy:
    indicators: List[IndicatorConfig]
    entry_rules: List[RuleCondition]
    exit_rules: List[RuleCondition]
    risk_management: RiskManagement
    trading_costs: TradingCosts = None  # New field for trading costs
    trading_sessions: List[TradingSession] = None
    entry_buy_group_operator: str = "AND"
    entry_sell_group_operator: str = "AND"
    exit_buy_group_operator: str = "AND"
    exit_sell_group_operator: str = "AND"

    def __post_init__(self):
        if self.trading_sessions is None:
            self.trading_sessions = [TradingSession.ALL]
        if self.trading_costs is None:
            self.trading_costs = TradingCosts()

    def is_within_trading_session(self, timestamp: datetime) -> bool:
        """
        Check if a given timestamp is within any of the strategy's trading sessions.

        Args:
            timestamp: The datetime to check

        Returns:
            bool: True if within trading session, False otherwise
        """
        if TradingSession.ALL in self.trading_sessions:
            return True

        current_time = timestamp.time()

        for session in self.trading_sessions:
            if session == TradingSession.ALL:
                continue

            start_time, end_time = SESSION_TIMES[session]

            # Handle sessions that cross midnight
            if start_time > end_time:
                if current_time >= start_time or current_time <= end_time:
                    return True
            else:
                if start_time <= current_time <= end_time:
                    return True

        return False

    def validate(self) -> bool:
        """
        Validate the strategy configuration.

        Returns:
            bool: True if strategy is valid, False otherwise

        Raises:
            ValueError: If validation fails with specific reason
        """
        # Validate trading sessions
        if not self.trading_sessions:
            raise ValueError("At least one trading session must be specified")

        for session in self.trading_sessions:
            if not isinstance(session, TradingSession):
                raise ValueError(f"Invalid trading session: {session}")

        # Validate indicators
        indicator_ids = set()
        for ind in self.indicators:
            if ind.id in indicator_ids:
                raise ValueError(f"Duplicate indicator ID: {ind.id}")
            indicator_ids.add(ind.id)

            # Validate indicator source
            if (ind.source != "price" and
                ind.source != "volume" and
                ind.source not in indicator_ids):
                raise ValueError(
                    f"Invalid source for indicator {ind.id}: {ind.source}")

        # Validate rules
        for rule in self.entry_rules + self.exit_rules:
            # Validate indicator references
            if (rule.indicator1 != "price" and
                rule.indicator1 not in indicator_ids):
                raise ValueError(
                    f"Invalid indicator1 reference: {rule.indicator1}")

            if (rule.compare_type == CompareType.INDICATOR and
                rule.indicator2 not in indicator_ids):
                raise ValueError(
                    f"Invalid indicator2 reference: {rule.indicator2}")

            # Validate price references
            if rule.indicator1 == "price":
                if rule.bar_ref not in ["close", "open", "high", "low"]:
                    raise ValueError(
                        f"Invalid bar_ref for price: {rule.bar_ref}")
            else:
                if rule.bar_ref != "":
                    raise ValueError(
                        "bar_ref should be empty for indicator references")

            # Validate compare type consistency
            if rule.compare_type == CompareType.INDICATOR:
                if rule.value is not None:
                    raise ValueError(
                        "value should be None when compare_type is indicator")
                if rule.indicator2 is None:
                    raise ValueError(
                        "indicator2 required when compare_type is indicator")
            else:  # VALUE
                if rule.indicator2 is not None:
                    raise ValueError(
                        "indicator2 should be None when compare_type is value")
                if rule.value is None:
                    raise ValueError(
                        "value required when compare_type is value")

        return True

    @classmethod
    def from_dict(cls, data: dict) -> 'Strategy':
        """Create a Strategy instance from a dictionary."""
        indicators = [
            IndicatorConfig(**ind) for ind in data.get('indicators', [])
        ]
        # Process entry rules with detailed logging
        entry_rules = []
        for i, rule in enumerate(data.get('entry_rules', [])):
            print(f"Processing entry rule {i}: {rule}")

            # Ensure trade_type is properly set
            if not rule.get('trade_type'):
                print(f"Warning: trade_type is empty in entry rule {i}, defaulting to 'long'")
                rule['trade_type'] = 'long'

            # Ensure compare_type is properly set
            if not rule.get('compare_type'):
                print(f"Warning: compare_type is empty in entry rule {i}, defaulting to 'indicator'")
                rule['compare_type'] = 'indicator'

            # Create the rule condition
            try:
                entry_rules.append(RuleCondition(**rule))
                print(f"Successfully created entry rule {i}")
            except Exception as e:
                print(f"Error creating entry rule {i}: {e}")
                print(f"Rule data: {rule}")
                # Create a default rule if possible
                if 'indicator1' in rule and 'operator' in rule:
                    fixed_rule = {
                        'trade_type': rule.get('trade_type', 'long'),
                        'indicator1': rule['indicator1'],
                        'operator': rule['operator'],
                        'compare_type': rule.get('compare_type', 'indicator'),
                        'indicator2': rule.get('indicator2'),
                        'value': rule.get('value'),
                        'logical_operator': rule.get('logical_operator', 'AND'),
                        'bar_ref': rule.get('bar_ref', 'close'),
                        'band': rule.get('band'),
                        'band2': rule.get('band2')
                    }
                    print(f"Attempting to create fixed entry rule: {fixed_rule}")
                    entry_rules.append(RuleCondition(**fixed_rule))

        # Process exit rules with detailed logging
        exit_rules = []
        for i, rule in enumerate(data.get('exit_rules', [])):
            print(f"Processing exit rule {i}: {rule}")

            # Ensure trade_type is properly set
            if not rule.get('trade_type'):
                print(f"Warning: trade_type is empty in exit rule {i}, defaulting to 'long'")
                rule['trade_type'] = 'long'

            # Ensure compare_type is properly set
            if not rule.get('compare_type'):
                print(f"Warning: compare_type is empty in exit rule {i}, defaulting to 'indicator'")
                rule['compare_type'] = 'indicator'

            # Create the rule condition
            try:
                exit_rules.append(RuleCondition(**rule))
                print(f"Successfully created exit rule {i}")
            except Exception as e:
                print(f"Error creating exit rule {i}: {e}")
                print(f"Rule data: {rule}")
                # Create a default rule if possible
                if 'indicator1' in rule and 'operator' in rule:
                    fixed_rule = {
                        'trade_type': rule.get('trade_type', 'long'),
                        'indicator1': rule['indicator1'],
                        'operator': rule['operator'],
                        'compare_type': rule.get('compare_type', 'indicator'),
                        'indicator2': rule.get('indicator2'),
                        'value': rule.get('value'),
                        'logical_operator': rule.get('logical_operator', 'AND'),
                        'bar_ref': rule.get('bar_ref', 'close'),
                        'band': rule.get('band'),
                        'band2': rule.get('band2')
                    }
                    print(f"Attempting to create fixed exit rule: {fixed_rule}")
                    exit_rules.append(RuleCondition(**fixed_rule))
        # Parse risk management
        risk_management_data = data.get('risk_management', {})
        print(f"Risk management data from frontend: {risk_management_data}")

        # Handle indicator-based stop loss if present
        indicator_based_sl = None
        if (risk_management_data.get('stopLossMethod') == 'indicator' or
            risk_management_data.get('stop_loss_method') == 'indicator'):
            # Try both camelCase and snake_case keys
            indicator_sl_data = risk_management_data.get('indicatorBasedSL') or risk_management_data.get('indicator_based_sl') or {}
            print(f"Indicator-based stop loss data: {indicator_sl_data}")
            if indicator_sl_data:
                indicator_based_sl = IndicatorBasedSL(
                    indicator=indicator_sl_data.get('indicator', ''),
                    parameters=indicator_sl_data.get('parameters', {})
                )

        # Extract risk-reward ratio with detailed logging
        risk_reward_ratio = risk_management_data.get('riskRewardRatio')
        if risk_reward_ratio is None:
            risk_reward_ratio = risk_management_data.get('risk_reward_ratio')
        if risk_reward_ratio is None:
            risk_reward_ratio = '2'  # Default value

        print(f"Risk-reward ratio from frontend (in Strategy.from_dict): {risk_reward_ratio} (type: {type(risk_reward_ratio)})")

        # Special handling for risk-reward ratio in format "1:1"
        if isinstance(risk_reward_ratio, str) and ":" in risk_reward_ratio:
            print(f"Found risk-reward ratio in format X:Y: {risk_reward_ratio}")

        # Ensure we're not using an empty string
        if risk_reward_ratio == "":
            risk_reward_ratio = "2"
            print(f"Empty risk-reward ratio, using default: {risk_reward_ratio}")

        # Create RiskManagement object with new fields
        risk_management = RiskManagement(
            # Old fields
            stop_loss=risk_management_data.get('stopLoss') or risk_management_data.get('stop_loss'),
            take_profit=risk_management_data.get('takeProfit') or risk_management_data.get('take_profit'),

            # New fields
            risk_percentage=risk_management_data.get('riskPercentage') or risk_management_data.get('risk_percentage', '1'),
            risk_reward_ratio=risk_reward_ratio,
            stop_loss_method=risk_management_data.get('stopLossMethod') or risk_management_data.get('stop_loss_method', 'fixed'),
            fixed_pips=risk_management_data.get('fixedPips') or risk_management_data.get('fixed_pips'),
            lot_size=risk_management_data.get('lotSize') or risk_management_data.get('lot_size'),
            indicator_based_sl=indicator_based_sl
        )

        print(f"Created RiskManagement object: stop_loss={risk_management.stop_loss}, take_profit={risk_management.take_profit}, risk_percentage={risk_management.risk_percentage}, risk_reward_ratio={risk_management.risk_reward_ratio}, stop_loss_method={risk_management.stop_loss_method}")

        # Convert trading sessions
        trading_sessions = [
            TradingSession(session)
            for session in data.get('trading_sessions', ['All'])
        ]

        # Parse trading costs
        trading_costs = TradingCosts(
            spread_pips=float(data.get('trading_costs', {}).get('spread_pips', 1.0)),
            commission_percentage=float(data.get('trading_costs', {}).get('commission_percentage', 0.0)),
            commission_fixed=float(data.get('trading_costs', {}).get('commission_fixed', 0.0))
        )

        return cls(
            indicators=indicators,
            entry_rules=entry_rules,
            exit_rules=exit_rules,
            risk_management=risk_management,
            trading_costs=trading_costs,
            trading_sessions=trading_sessions,
            entry_buy_group_operator=data.get('entry_buy_group_operator', 'AND'),
            entry_sell_group_operator=data.get('entry_sell_group_operator', 'AND'),
            exit_buy_group_operator=data.get('exit_buy_group_operator', 'AND'),
            exit_sell_group_operator=data.get('exit_sell_group_operator', 'AND')
        )