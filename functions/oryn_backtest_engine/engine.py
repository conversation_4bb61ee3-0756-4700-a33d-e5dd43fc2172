import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime

from .models.strategy import (
    Strategy,
    TradeType,
    CompareType,
    Operator,
    RuleCondition,
    RiskManagement,
    TradingCosts,
    IndicatorConfig,
    StopLossMethod,
    IndicatorType
)
from .indicators.technical import SMA, EMA, RSI, MACD
from .indicators.volatility import ATR, BollingerBands
from .indicators.support_resistance import SupportResistance
from .utils.calculations import calculate_returns, calculate_sharpe_ratio

class Position:
    def __init__(self,
                 entry_price: float,
                 entry_time: datetime,
                 trade_type: TradeType,
                 size: float = 1.0,
                 stop_loss: Optional[float] = None,
                 take_profit: Optional[float] = None,
                 spread_pips: float = 1.0,
                 commission_percentage: float = 0.0,
                 commission_fixed: float = 0.0,
                 entry_rule: Optional[str] = None):
        self.entry_price = entry_price
        self.entry_time = entry_time
        self.trade_type = trade_type
        self.size = size
        self.stop_loss = stop_loss
        self.take_profit = take_profit

        # Trading costs
        self.spread_pips = spread_pips
        self.commission_percentage = commission_percentage
        self.commission_fixed = commission_fixed

        self.exit_price: Optional[float] = None
        self.exit_time: Optional[datetime] = None
        self.pnl: Optional[float] = None
        self.costs: Optional[float] = None
        self.net_pnl: Optional[float] = None
        self.entry_rule: Optional[str] = entry_rule
        self.exit_rule: Optional[str] = None
        self.exit_reason: Optional[str] = None

    def _calculate_costs(self, exit_price: float) -> float:
        """Calculate total trading costs."""
        # Convert pips to price points (assuming 4 decimal places)
        pip_value = 0.0001
        spread_cost = self.spread_pips * pip_value * self.size

        # Calculate commission
        position_value = (self.entry_price + exit_price) * self.size / 2  # Average position value
        percentage_commission = position_value * (self.commission_percentage / 100)

        # Total costs (spread + percentage commission + fixed commission)
        total_costs = spread_cost + percentage_commission + self.commission_fixed

        return total_costs

    def close(self, exit_price: float, exit_time: datetime, exit_rule: Optional[str] = None, exit_reason: Optional[str] = None) -> float:
        """Close the position and calculate PnL."""
        self.exit_price = exit_price
        self.exit_time = exit_time
        self.exit_rule = exit_rule
        self.exit_reason = exit_reason

        # Calculate gross PnL
        if self.trade_type == TradeType.LONG:
            self.pnl = (exit_price - self.entry_price) * self.size
        else:  # SHORT
            self.pnl = (self.entry_price - exit_price) * self.size

        # Calculate trading costs
        self.costs = self._calculate_costs(exit_price)

        # Calculate net PnL
        self.net_pnl = self.pnl - self.costs

        return self.net_pnl

class OrynBacktestEngine:
    """Main backtesting engine for Oryn Trading Platform."""

    def __init__(self, data: pd.DataFrame, initial_capital: float = 100000.0):
        """
        Initialize the backtesting engine.

        Args:
            data: DataFrame with OHLCV data
                 Required columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            initial_capital: Starting capital (default: 100,000)
        """
        self._validate_data(data)
        self.data = data
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trades = []
        self.equity_curve = []

        self.indicators: Dict[str, Union[SMA, EMA, RSI, MACD]] = {}
        self.positions: List[Position] = []  # Closed positions
        self.open_positions: List[Position] = []  # Currently open positions

        # Performance metrics
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0
        self.total_costs = 0.0
        self.net_pnl = 0.0
        self.max_drawdown = 0.0
        self.returns = []

    def _validate_data(self, data: pd.DataFrame) -> None:
        """Validate input data has required columns."""
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing = [col for col in required_columns if col not in data.columns]
        if missing:
            raise ValueError(f"Missing required columns: {missing}")

    def _setup_indicators(self, strategy: Strategy) -> None:
        """Initialize all indicators defined in the strategy."""
        # First, prepare OHLC data for indicators that need it
        ohlc_data = np.column_stack((
            self.data['open'].values,
            self.data['high'].values,
            self.data['low'].values,
            self.data['close'].values
        ))

        # Setup indicators from strategy configuration
        for ind_config in strategy.indicators:
            if ind_config.source == "price" or ind_config.source == "close":
                # Both "price" and "close" refer to the close price data
                source_data = self.data['close'].values
                print(f"Using close price data as source for indicator {ind_config.id}")
            elif ind_config.source == "open":
                source_data = self.data['open'].values
                print(f"Using open price data as source for indicator {ind_config.id}")
            elif ind_config.source == "high":
                source_data = self.data['high'].values
                print(f"Using high price data as source for indicator {ind_config.id}")
            elif ind_config.source == "low":
                source_data = self.data['low'].values
                print(f"Using low price data as source for indicator {ind_config.id}")
            elif ind_config.source == "volume":
                source_data = self.data['volume'].values
                print(f"Using volume data as source for indicator {ind_config.id}")
            else:
                # Source is another indicator
                try:
                    source_data = self.indicators[ind_config.source].get_series()
                    print(f"Using indicator {ind_config.source} as source for indicator {ind_config.id}")
                except KeyError:
                    print(f"Error: Source indicator '{ind_config.source}' not found for indicator {ind_config.id}")
                    print(f"Available indicators: {list(self.indicators.keys())}")
                    print(f"Available price data columns: {list(self.data.columns)}")
                    raise

            if ind_config.type == "SMA":
                # Ensure parameters are converted to the correct types
                period = int(ind_config.parameters.get('period', 14))
                print(f"Creating SMA indicator with period={period}")

                self.indicators[ind_config.id] = SMA(
                    source_data,
                    period=period
                )
            elif ind_config.type == "EMA":
                # Ensure parameters are converted to the correct types
                period = int(ind_config.parameters.get('period', 14))
                print(f"Creating EMA indicator with period={period}")

                self.indicators[ind_config.id] = EMA(
                    source_data,
                    period=period
                )
            elif ind_config.type == "RSI":
                # Ensure parameters are converted to the correct types
                period = int(ind_config.parameters.get('period', 14))
                print(f"Creating RSI indicator with period={period}")

                self.indicators[ind_config.id] = RSI(
                    source_data,
                    period=period
                )
            elif ind_config.type == "MACD":
                # Ensure parameters are converted to the correct types
                fast_period = int(ind_config.parameters.get('fast_period', 12))
                slow_period = int(ind_config.parameters.get('slow_period', 26))
                signal_period = int(ind_config.parameters.get('signal_period', 9))
                print(f"Creating MACD indicator with fast_period={fast_period}, slow_period={slow_period}, signal_period={signal_period}")

                self.indicators[ind_config.id] = MACD(
                    source_data,
                    fast_period=fast_period,
                    slow_period=slow_period,
                    signal_period=signal_period
                )
            elif ind_config.type == "ATR":
                # Ensure parameters are converted to the correct types
                period = int(ind_config.parameters.get('period', 14))
                multiplier = float(ind_config.parameters.get('multiplier', 1.0))
                print(f"Creating ATR indicator with period={period}, multiplier={multiplier} (type: {type(multiplier)})")

                self.indicators[ind_config.id] = ATR(
                    ohlc_data,
                    period=period,
                    multiplier=multiplier
                )
            elif ind_config.type == "BollingerBands":
                # Ensure parameters are converted to the correct types
                period = int(ind_config.parameters.get('period', 20))
                std_dev = float(ind_config.parameters.get('devfactor', 2.0))
                print(f"Creating BollingerBands indicator with period={period}, std_dev={std_dev}")

                self.indicators[ind_config.id] = BollingerBands(
                    source_data,
                    period=period,
                    std_dev=std_dev
                )
            elif ind_config.type == "SupportResistance":
                # Ensure parameters are converted to the correct types
                left_bars = int(ind_config.parameters.get('left', 10))
                right_bars = int(ind_config.parameters.get('right', 10))
                print(f"Creating SupportResistance indicator with left_bars={left_bars}, right_bars={right_bars}")

                # SupportResistance always uses OHLC data regardless of source
                self.indicators[ind_config.id] = SupportResistance(
                    ohlc_data,
                    left_bars=left_bars,
                    right_bars=right_bars
                )
            else:
                raise ValueError(f"Unsupported indicator type: {ind_config.type}")

        # Setup risk management indicators if needed
        if (strategy.risk_management.stop_loss_method == StopLossMethod.INDICATOR and
            strategy.risk_management.indicator_based_sl):

            indicator_sl = strategy.risk_management.indicator_based_sl
            indicator_type = indicator_sl.indicator
            params = indicator_sl.parameters

            # Check if indicator_type matches any of the IndicatorType enum values
            valid_indicator_types = [t.value for t in IndicatorType]
            print(f"Valid indicator types: {valid_indicator_types}")
            print(f"Current indicator type: {indicator_type}, Is valid: {indicator_type in valid_indicator_types}")

            # If indicator type is not valid, try to map it to a valid one
            if indicator_type not in valid_indicator_types:
                # Try to map common variations
                if indicator_type == "support_resistance" or indicator_type == "supportresistance" or indicator_type == "sr":
                    indicator_type = IndicatorType.SUPPORT_RESISTANCE.value
                    print(f"Mapped indicator type to {indicator_type}")
                elif indicator_type == "bollinger" or indicator_type == "bb":
                    indicator_type = IndicatorType.BOLLINGER_BANDS.value
                    print(f"Mapped indicator type to {indicator_type}")
                elif indicator_type == "atr":
                    indicator_type = IndicatorType.ATR.value
                    print(f"Mapped indicator type to {indicator_type}")

            # Create a unique ID for the risk management indicator
            risk_indicator_id = f"risk_management_{indicator_type}"
            print(f"Setting up risk management indicator: {risk_indicator_id}")
            print(f"Indicator type: {indicator_type}, Parameters: {params}")

            try:
                if indicator_type == IndicatorType.ATR:
                    period = int(params.get('period', 14))
                    multiplier = float(params.get('multiplier', 1.0))
                    print(f"Creating ATR risk indicator with period={period}, multiplier={multiplier}")

                    self.indicators[risk_indicator_id] = ATR(
                        ohlc_data,
                        period=period,
                        multiplier=multiplier
                    )
                elif indicator_type == IndicatorType.BOLLINGER_BANDS:
                    period = int(params.get('period', 20))
                    std_dev = float(params.get('stdDev', 2.0))
                    print(f"Creating BollingerBands risk indicator with period={period}, std_dev={std_dev}")

                    self.indicators[risk_indicator_id] = BollingerBands(
                        self.data['close'].values,
                        period=period,
                        std_dev=std_dev
                    )
                elif indicator_type == IndicatorType.SUPPORT_RESISTANCE:
                    # Ensure parameters are converted to integers
                    try:
                        left_bars = int(params.get('left', 10))
                    except (ValueError, TypeError):
                        print(f"Error converting left parameter to int: {params.get('left')}. Using default value 10.")
                        left_bars = 10

                    try:
                        right_bars = int(params.get('right', 10))
                    except (ValueError, TypeError):
                        print(f"Error converting right parameter to int: {params.get('right')}. Using default value 10.")
                        right_bars = 10

                    print(f"Creating SupportResistance risk indicator with left_bars={left_bars}, right_bars={right_bars}")

                    self.indicators[risk_indicator_id] = SupportResistance(
                        ohlc_data,
                        left_bars=left_bars,
                        right_bars=right_bars
                    )
                    print(f"Added SupportResistance indicator with ID: {risk_indicator_id}")
                else:
                    print(f"Warning: Unsupported risk indicator type: {indicator_type}")
            except Exception as e:
                print(f"Error setting up risk management indicator: {e}")
                print(f"Using default stop loss instead")

    def _evaluate_condition(self,
                          rule: RuleCondition,
                          index: int,
                          prev_index: int) -> bool:
        """Evaluate a single rule condition."""
        # Debug the rule being evaluated
        print(f"Evaluating rule: trade_type={rule.trade_type}, indicator1={rule.indicator1}, operator={rule.operator}, compare_type={rule.compare_type}, indicator2={rule.indicator2}, band={rule.band}, band2={rule.band2}")

        # Get value1 (indicator1 or price)
        if rule.indicator1 == "price":
            value1 = self.data[rule.bar_ref].iloc[index]
            prev_value1 = self.data[rule.bar_ref].iloc[prev_index]
            print(f"Using price data for indicator1: current={value1}, previous={prev_value1}")
        else:
            # Check if this is a Bollinger Bands indicator and a specific band is requested
            if rule.band and rule.indicator1 in self.indicators and isinstance(self.indicators[rule.indicator1], BollingerBands):
                bb_indicator = self.indicators[rule.indicator1]
                lower, middle, upper = bb_indicator.get_bands(index)
                prev_lower, prev_middle, prev_upper = bb_indicator.get_bands(prev_index)

                print(f"Bollinger Bands for indicator1: lower={lower}, middle={middle}, upper={upper}")

                # Select the appropriate band
                if rule.band.lower() == "lower":
                    value1 = lower
                    prev_value1 = prev_lower
                elif rule.band.lower() == "upper":
                    value1 = upper
                    prev_value1 = prev_upper
                else:  # Default to middle band
                    value1 = middle
                    prev_value1 = prev_middle

                print(f"Using Bollinger Band '{rule.band}' for indicator1: current={value1}, previous={prev_value1}")
            else:
                if rule.indicator1 in self.indicators:
                    value1 = self.indicators[rule.indicator1].get_value(index)
                    prev_value1 = self.indicators[rule.indicator1].get_value(prev_index)
                    print(f"Using indicator {rule.indicator1} for indicator1: current={value1}, previous={prev_value1}")
                else:
                    print(f"ERROR: Indicator {rule.indicator1} not found in available indicators: {list(self.indicators.keys())}")
                    return False

        # Get value2 (indicator2 or static value)
        if rule.compare_type == CompareType.INDICATOR:
            # Check if this is a Bollinger Bands indicator and a specific band is requested
            if rule.band2 and rule.indicator2 in self.indicators and isinstance(self.indicators[rule.indicator2], BollingerBands):
                bb_indicator = self.indicators[rule.indicator2]
                lower, middle, upper = bb_indicator.get_bands(index)
                prev_lower, prev_middle, prev_upper = bb_indicator.get_bands(prev_index)

                print(f"Bollinger Bands for indicator2: lower={lower}, middle={middle}, upper={upper}")

                # Select the appropriate band
                if rule.band2.lower() == "lower":
                    value2 = lower
                    prev_value2 = prev_lower
                elif rule.band2.lower() == "upper":
                    value2 = upper
                    prev_value2 = prev_upper
                else:  # Default to middle band
                    value2 = middle
                    prev_value2 = prev_middle

                print(f"Using Bollinger Band '{rule.band2}' for indicator2: current={value2}, previous={prev_value2}")
            else:
                if rule.indicator2 in self.indicators:
                    value2 = self.indicators[rule.indicator2].get_value(index)
                    prev_value2 = self.indicators[rule.indicator2].get_value(prev_index)
                    print(f"Using indicator {rule.indicator2} for indicator2: current={value2}, previous={prev_value2}")
                else:
                    print(f"ERROR: Indicator {rule.indicator2} not found in available indicators: {list(self.indicators.keys())}")
                    return False
        else:  # VALUE
            try:
                value2 = float(rule.value) if rule.value else 0.0
                prev_value2 = value2
                print(f"Using static value for value2: {value2}")
            except (ValueError, TypeError) as e:
                print(f"ERROR: Could not convert value '{rule.value}' to float: {e}")
                return False

        # Debug the operator evaluation
        print(f"Evaluating operator: {rule.operator}")
        print(f"Current values: value1={value1}, value2={value2}")
        print(f"Previous values: prev_value1={prev_value1}, prev_value2={prev_value2}")

        # Check for None values - indicator might not have enough data yet
        if value1 is None or value2 is None or prev_value1 is None or prev_value2 is None:
            print(f"WARNING: One or more values are None, skipping condition evaluation")
            print(f"This is normal during the warm-up period of indicators")
            return False

        result = False

        try:
            # Handle crossing conditions
            if rule.operator == Operator.CROSSING_ABOVE:
                result = prev_value1 <= prev_value2 and value1 > value2
                print(f"CROSSING_ABOVE: prev_value1 <= prev_value2 ({prev_value1} <= {prev_value2}) = {prev_value1 <= prev_value2}")
                print(f"CROSSING_ABOVE: value1 > value2 ({value1} > {value2}) = {value1 > value2}")
                print(f"CROSSING_ABOVE result: {result}")
            elif rule.operator == Operator.CROSSING_BELOW:
                result = prev_value1 >= prev_value2 and value1 < value2
                print(f"CROSSING_BELOW: prev_value1 >= prev_value2 ({prev_value1} >= {prev_value2}) = {prev_value1 >= prev_value2}")
                print(f"CROSSING_BELOW: value1 < value2 ({value1} < {value2}) = {value1 < value2}")
                print(f"CROSSING_BELOW result: {result}")

            # Handle simple comparisons
            elif rule.operator == Operator.GREATER_THAN:
                result = value1 > value2
                print(f"GREATER_THAN: value1 > value2 ({value1} > {value2}) = {result}")
            elif rule.operator == Operator.LESS_THAN:
                result = value1 < value2
                print(f"LESS_THAN: value1 < value2 ({value1} < {value2}) = {result}")
            elif rule.operator == Operator.EQUALS:
                result = abs(value1 - value2) < 1e-10
                print(f"EQUALS: abs(value1 - value2) < 1e-10 (abs({value1} - {value2}) < 1e-10) = {result}")
            elif rule.operator == Operator.GREATER_EQUAL:
                result = value1 >= value2
                print(f"GREATER_EQUAL: value1 >= value2 ({value1} >= {value2}) = {result}")
            elif rule.operator == Operator.LESS_EQUAL:
                result = value1 <= value2
                print(f"LESS_EQUAL: value1 <= value2 ({value1} <= {value2}) = {result}")
            else:
                error_msg = f"Unsupported operator: {rule.operator}"
                print(f"ERROR: {error_msg}")
                raise ValueError(error_msg)
        except Exception as e:
            print(f"ERROR in operator evaluation: {e}")
            return False

        print(f"Final result for operator {rule.operator}: {result}")
        return result

    def _evaluate_rules(self,
                       rules: List[RuleCondition],
                       group_operator: str,
                       index: int) -> bool:
        """Evaluate a group of rules with logical operator."""
        if not rules:
            return False

        prev_index = max(0, index - 1)
        results = [
            self._evaluate_condition(rule, index, prev_index)
            for rule in rules
        ]

        if group_operator == "AND":
            return all(results)
        elif group_operator == "OR":
            return any(results)
        else:
            raise ValueError(f"Unsupported group operator: {group_operator}")

    def _calculate_stop_loss_legacy(self, entry_price: float, stop_loss_str: str, is_long: bool = True) -> float:
        """
        Calculate stop loss price from string specification (legacy method).

        Args:
            entry_price: Entry price of the trade
            stop_loss_str: Stop loss as string (e.g., "2%" or "20pips")
            is_long: True for long trades, False for short trades

        For LONG trades: stop loss is BELOW entry price
        For SHORT trades: stop loss is ABOVE entry price
        """
        try:
            if "%" in stop_loss_str:
                percentage = float(stop_loss_str.replace("%", "")) / 100
                if is_long:
                    return entry_price * (1 - percentage)  # Below entry for longs
                else:
                    return entry_price * (1 + percentage)  # Above entry for shorts
            elif "pips" in stop_loss_str:
                pips = float(stop_loss_str.replace("pips", "").strip())
                pip_value = 0.0001
                if is_long:
                    return entry_price - (pips * pip_value)  # Below entry for longs
                else:
                    return entry_price + (pips * pip_value)  # Above entry for shorts
            else:
                raise ValueError(f"Invalid stop loss format: {stop_loss_str}")
        except Exception as e:
            print(f"Error calculating stop loss: {e}")
            print(f"Inputs: entry_price={entry_price}, stop_loss_str={stop_loss_str}, is_long={is_long}")
            raise

    def _calculate_indicator_stop_loss(self, index: int, entry_price: float, strategy: Strategy, is_long: bool) -> float:
        """
        Calculate stop loss price based on indicator.

        Args:
            index: Current index in the data
            entry_price: Entry price of the trade
            strategy: Strategy configuration
            is_long: True for long trades, False for short trades

        Returns:
            float: Stop loss price
        """
        print(f"DEBUG: Checking indicator-based stop loss. Strategy risk management: {strategy.risk_management}")
        print(f"DEBUG: indicator_based_sl: {strategy.risk_management.indicator_based_sl}")

        if not strategy.risk_management.indicator_based_sl:
            # Default to 2% of entry price if no indicator specified
            print(f"No indicator-based stop loss specified. Using default 2% stop loss.")
            # Return default value instead of raising an exception
            return entry_price * (0.98 if is_long else 1.02)

        indicator_type = strategy.risk_management.indicator_based_sl.indicator
        risk_indicator_id = f"risk_management_{indicator_type}"
        print(f"Calculating indicator-based stop loss using {indicator_type} (ID: {risk_indicator_id})")

        # Check if indicator_type matches any of the IndicatorType enum values
        valid_indicator_types = [t.value for t in IndicatorType]
        print(f"Valid indicator types: {valid_indicator_types}")
        print(f"Current indicator type: {indicator_type}, Is valid: {indicator_type in valid_indicator_types}")

        # If indicator type is not valid, try to map it to a valid one
        if indicator_type not in valid_indicator_types:
            # Try to map common variations
            if indicator_type == "support_resistance" or indicator_type == "supportresistance" or indicator_type == "sr":
                indicator_type = IndicatorType.SUPPORT_RESISTANCE.value
                risk_indicator_id = f"risk_management_{indicator_type}"
                print(f"Mapped indicator type to {indicator_type}, new ID: {risk_indicator_id}")
            elif indicator_type == "bollinger" or indicator_type == "bb":
                indicator_type = IndicatorType.BOLLINGER_BANDS.value
                risk_indicator_id = f"risk_management_{indicator_type}"
                print(f"Mapped indicator type to {indicator_type}, new ID: {risk_indicator_id}")
            elif indicator_type == "atr":
                indicator_type = IndicatorType.ATR.value
                risk_indicator_id = f"risk_management_{indicator_type}"
                print(f"Mapped indicator type to {indicator_type}, new ID: {risk_indicator_id}")

        # Check if indicator exists
        if risk_indicator_id not in self.indicators:
            print(f"Warning: Risk management indicator {risk_indicator_id} not found in available indicators: {list(self.indicators.keys())}")
            return entry_price * (0.98 if is_long else 1.02)

        indicator = self.indicators[risk_indicator_id]

        try:
            if indicator_type == IndicatorType.ATR:
                # Use ATR.get_stop_loss method
                stop_loss = indicator.get_stop_loss(index, entry_price, is_long)
                print(f"ATR-based stop loss calculated: {stop_loss} (entry: {entry_price}, distance: {abs(entry_price - stop_loss)})")
                return stop_loss

            elif indicator_type == IndicatorType.BOLLINGER_BANDS:
                # Use BollingerBands.get_stop_loss method
                stop_loss = indicator.get_stop_loss(index, entry_price, is_long)
                print(f"Bollinger Bands-based stop loss calculated: {stop_loss} (entry: {entry_price}, distance: {abs(entry_price - stop_loss)})")
                return stop_loss

            elif indicator_type == IndicatorType.SUPPORT_RESISTANCE:
                # Use SupportResistance.get_stop_loss method
                print(f"Calculating Support & Resistance-based stop loss for {'LONG' if is_long else 'SHORT'} trade at price {entry_price}")

                # Get the indicator value to see what levels are available
                sr_value = indicator.get_value(index)
                if sr_value:
                    print(f"Support & Resistance levels at index {index}: Support={sr_value.get('support')}, Resistance={sr_value.get('resistance')}")

                stop_loss = indicator.get_stop_loss(index, entry_price, is_long)
                print(f"Support & Resistance-based stop loss calculated: {stop_loss} (entry: {entry_price}, distance: {abs(entry_price - stop_loss)})")
                return stop_loss

            else:
                print(f"Unsupported indicator type: {indicator_type}. Using default 2% stop loss.")
                return entry_price * (0.98 if is_long else 1.02)

        except Exception as e:
            print(f"Error calculating indicator-based stop loss: {e}")
            print(f"Using default 2% stop loss instead.")
            return entry_price * (0.98 if is_long else 1.02)

    def _calculate_stop_loss(self, index: int, entry_price: float, strategy: Strategy, is_long: bool) -> float:
        """
        Calculate stop loss price based on strategy risk management settings.

        Args:
            index: Current index in the data
            entry_price: Entry price of the trade
            strategy: Strategy configuration
            is_long: True for long trades, False for short trades

        Returns:
            float: Stop loss price
        """
        # Handle different stop loss methods
        print(f"DEBUG: stop_loss_method={strategy.risk_management.stop_loss_method}, type={type(strategy.risk_management.stop_loss_method)}")
        print(f"DEBUG: StopLossMethod.FIXED={StopLossMethod.FIXED}, type={type(StopLossMethod.FIXED)}")
        print(f"DEBUG: Comparison result: {strategy.risk_management.stop_loss_method == StopLossMethod.FIXED}")
        print(f"DEBUG: fixed_pips={strategy.risk_management.fixed_pips}, type={type(strategy.risk_management.fixed_pips)}")
        print(f"DEBUG: stop_loss={strategy.risk_management.stop_loss}, type={type(strategy.risk_management.stop_loss)}")

        # Force the stop_loss_method to be a StopLossMethod enum if it's a string
        stop_loss_method = strategy.risk_management.stop_loss_method
        if isinstance(stop_loss_method, str):
            try:
                stop_loss_method = StopLossMethod(stop_loss_method)
                print(f"Converted string stop_loss_method '{strategy.risk_management.stop_loss_method}' to enum: {stop_loss_method}")
            except ValueError as e:
                print(f"Error converting stop_loss_method: {e}")

        if stop_loss_method == StopLossMethod.FIXED:
            # Fixed pips stop loss
            if strategy.risk_management.fixed_pips:
                try:
                    # Log the fixed_pips value for debugging
                    print(f"Fixed pips value from strategy: '{strategy.risk_management.fixed_pips}' (type: {type(strategy.risk_management.fixed_pips)})")

                    # Convert to float, handling potential string formatting issues
                    fixed_pips_str = str(strategy.risk_management.fixed_pips).strip()
                    pips = float(fixed_pips_str)

                    # Validate the pips value
                    if pips <= 0:
                        print(f"Warning: Invalid fixed pips value: {pips}. Using default 20 pips.")
                        pips = 20.0
                    elif pips > 500:
                        print(f"Warning: Fixed pips value {pips} is unusually large. Capping at 500 pips.")
                        pips = 500.0

                    pip_value = 0.0001  # For forex

                    # Calculate stop loss price
                    stop_loss_price = entry_price - (pips * pip_value) if is_long else entry_price + (pips * pip_value)

                    # Calculate and log the actual pips distance
                    actual_pips_distance = abs(entry_price - stop_loss_price) * 10000
                    print(f"Fixed pips stop loss calculated: {stop_loss_price} (entry: {entry_price}, pips: {pips}, actual distance: {actual_pips_distance:.1f} pips)")

                    # Verify the stop loss is the correct distance
                    if abs(actual_pips_distance - pips) > 1:  # Allow for small floating point differences
                        print(f"WARNING: Stop loss distance ({actual_pips_distance:.1f} pips) doesn't match requested pips ({pips}). Recalculating...")
                        # Force the correct distance
                        if is_long:
                            stop_loss_price = entry_price - (pips * 0.0001)
                        else:
                            stop_loss_price = entry_price + (pips * 0.0001)

                        # Verify again
                        actual_pips_distance = abs(entry_price - stop_loss_price) * 10000
                        print(f"Recalculated stop loss: {stop_loss_price} (distance: {actual_pips_distance:.1f} pips)")

                    return stop_loss_price
                except (ValueError, TypeError) as e:
                    print(f"Error parsing fixed pips value '{strategy.risk_management.fixed_pips}': {e}")
                    print("Using default 20 pips for stop loss")
                    pips = 20.0
                    pip_value = 0.0001
                    stop_loss_price = entry_price - (pips * pip_value) if is_long else entry_price + (pips * pip_value)

                    # Log the actual pips distance
                    actual_pips_distance = abs(entry_price - stop_loss_price) * 10000
                    print(f"Default fixed pips stop loss: {stop_loss_price} (entry: {entry_price}, pips: {pips}, actual distance: {actual_pips_distance:.1f} pips)")

                    return stop_loss_price

            # Backward compatibility with old stop_loss field
            elif strategy.risk_management.stop_loss:
                return self._calculate_stop_loss_legacy(entry_price, strategy.risk_management.stop_loss, is_long)

            else:
                # Default to 2% of entry price if no stop loss specified
                print("No fixed pips or legacy stop loss specified. Using default 2% stop loss.")
                return entry_price * (0.98 if is_long else 1.02)

        elif stop_loss_method == StopLossMethod.RISK:
            # Risk-based stop loss calculation
            # Formula: Stop Loss = (Account Balance × Risk%) ÷ (Lot Size × Pip Value)
            if strategy.risk_management.lot_size and strategy.risk_management.risk_percentage:
                try:
                    # Parse lot size
                    lot_size_str = str(strategy.risk_management.lot_size).strip()
                    lot_size = float(lot_size_str)

                    # Parse risk percentage
                    risk_percentage_str = str(strategy.risk_management.risk_percentage).strip()
                    risk_percentage = float(risk_percentage_str) / 100  # Convert to decimal

                    # Calculate risk amount
                    risk_amount = self.current_capital * risk_percentage

                    # Calculate pip value (standard for forex)
                    pip_value = 0.0001

                    # Calculate stop loss distance in price units
                    # For a standard lot (1.0), each pip is worth $10 for most forex pairs
                    # For a mini lot (0.1), each pip is worth $1
                    # For a micro lot (0.01), each pip is worth $0.10
                    pip_monetary_value = lot_size * 10  # $10 per pip per standard lot

                    # Calculate required pips to risk the specified amount
                    if pip_monetary_value > 0:
                        required_pips = risk_amount / pip_monetary_value

                        # Cap at reasonable values
                        if required_pips <= 0:
                            print(f"Warning: Calculated required pips is invalid: {required_pips}. Using default 20 pips.")
                            required_pips = 20.0
                        elif required_pips > 500:
                            print(f"Warning: Calculated required pips is too large: {required_pips}. Capping at 500 pips.")
                            required_pips = 500.0

                        # Convert pips to price distance
                        price_distance = required_pips * pip_value

                        # Calculate stop loss price
                        stop_loss_price = entry_price - price_distance if is_long else entry_price + price_distance

                        # Log the calculation
                        print(f"Risk-based stop loss calculation:")
                        print(f"  Account balance: ${self.current_capital:.2f}")
                        print(f"  Risk percentage: {risk_percentage*100:.2f}%")
                        print(f"  Risk amount: ${risk_amount:.2f}")
                        print(f"  Lot size: {lot_size}")
                        print(f"  Pip monetary value: ${pip_monetary_value:.2f} per pip")
                        print(f"  Required pips: {required_pips:.1f}")
                        print(f"  Stop loss price: {stop_loss_price} ({required_pips:.1f} pips from entry)")

                        return stop_loss_price
                    else:
                        print(f"Warning: Invalid pip monetary value: {pip_monetary_value}. Using default 2% stop loss.")
                        return entry_price * (0.98 if is_long else 1.02)
                except (ValueError, TypeError, ZeroDivisionError) as e:
                    print(f"Error calculating risk-based stop loss: {e}")
                    print(f"Using default 2% stop loss")
                    return entry_price * (0.98 if is_long else 1.02)
            else:
                print(f"Missing lot size or risk percentage for risk-based stop loss. Using default 2% stop loss.")
                return entry_price * (0.98 if is_long else 1.02)

        elif stop_loss_method == StopLossMethod.INDICATOR:
            # Indicator-based stop loss
            return self._calculate_indicator_stop_loss(index, entry_price, strategy, is_long)

        else:
            # Default to 2% of entry price
            return entry_price * (0.98 if is_long else 1.02)

    def _calculate_take_profit_legacy(self, entry_price: float, take_profit_str: str, is_long: bool = True) -> float:
        """
        Calculate take profit price from string specification (legacy method).

        Args:
            entry_price: Entry price of the trade
            take_profit_str: Take profit as string (e.g., "2%" or "20pips")
            is_long: True for long trades, False for short trades

        For LONG trades: take profit is ABOVE entry price
        For SHORT trades: take profit is BELOW entry price
        """
        try:
            if "%" in take_profit_str:
                percentage = float(take_profit_str.replace("%", "")) / 100
                if is_long:
                    return entry_price * (1 + percentage)  # Above entry for longs
                else:
                    return entry_price * (1 - percentage)  # Below entry for shorts
            elif "pips" in take_profit_str:
                pips = float(take_profit_str.replace("pips", "").strip())
                pip_value = 0.0001
                if is_long:
                    return entry_price + (pips * pip_value)  # Above entry for longs
                else:
                    return entry_price - (pips * pip_value)  # Below entry for shorts
            else:
                raise ValueError(f"Invalid take profit format: {take_profit_str}")
        except Exception as e:
            print(f"Error calculating take profit: {e}")
            print(f"Inputs: entry_price={entry_price}, take_profit_str={take_profit_str}, is_long={is_long}")
            raise

    def _calculate_take_profit(self, _index: int, entry_price: float, stop_loss_price: float, strategy: Strategy, is_long: bool) -> float:
        """
        Calculate take profit price based on strategy risk management settings.

        Args:
            _index: Current index in the data (unused but kept for consistent method signature)
            entry_price: Entry price of the trade
            stop_loss_price: Stop loss price
            strategy: Strategy configuration
            is_long: True for long trades, False for short trades

        Returns:
            float: Take profit price
        """
        print(f"\nCalculating take profit for {'LONG' if is_long else 'SHORT'} trade:")
        print(f"Entry price: {entry_price}")
        print(f"Stop loss price: {stop_loss_price}")

        # Get risk-reward ratio with fallback to default
        risk_reward_ratio_str = strategy.risk_management.risk_reward_ratio
        if risk_reward_ratio_str is None or risk_reward_ratio_str == "":
            risk_reward_ratio_str = "2"
        print(f"Risk-reward ratio from strategy (raw): {risk_reward_ratio_str} (type: {type(risk_reward_ratio_str)})")

        # Backward compatibility with old take_profit field - only use if explicitly told to
        if not risk_reward_ratio_str and strategy.risk_management.take_profit:
            print(f"WARNING: Using legacy take profit format: {strategy.risk_management.take_profit}")
            print(f"This is deprecated and will be removed in a future version. Please use risk-reward ratio instead.")
            take_profit = self._calculate_take_profit_legacy(entry_price, strategy.risk_management.take_profit, is_long)
            print(f"Calculated take profit (legacy): {take_profit}")
            return take_profit

        try:
            # Handle different formats (e.g., "2", "2:1", "2.5", 2, etc.)
            if isinstance(risk_reward_ratio_str, str):
                if ":" in risk_reward_ratio_str:
                    parts = risk_reward_ratio_str.split(":")
                    if len(parts) == 2:
                        try:
                            risk_reward_ratio = float(parts[0]) / float(parts[1])
                        except (ValueError, ZeroDivisionError):
                            print(f"Error parsing risk-reward ratio '{risk_reward_ratio_str}'. Using default 2.0")
                            risk_reward_ratio = 2.0
                    else:
                        print(f"Invalid risk-reward ratio format '{risk_reward_ratio_str}'. Using default 2.0")
                        risk_reward_ratio = 2.0
                else:
                    try:
                        risk_reward_ratio = float(risk_reward_ratio_str)
                    except ValueError:
                        print(f"Error converting risk-reward ratio '{risk_reward_ratio_str}' to float. Using default 2.0")
                        risk_reward_ratio = 2.0
            else:
                # Handle numeric types directly
                try:
                    risk_reward_ratio = float(risk_reward_ratio_str)
                except (ValueError, TypeError):
                    print(f"Error converting risk-reward ratio '{risk_reward_ratio_str}' to float. Using default 2.0")
                    risk_reward_ratio = 2.0

            print(f"Parsed risk-reward ratio: {risk_reward_ratio}")

            # Calculate price distance based on stop loss
            price_distance = abs(entry_price - stop_loss_price)
            price_distance_pips = price_distance * 10000
            print(f"Price distance to stop loss: {price_distance} ({price_distance_pips:.1f} pips)")

            # Verify the stop loss distance is reasonable
            if price_distance_pips > 500:
                print(f"WARNING: Stop loss distance ({price_distance_pips:.1f} pips) is unusually large. This may be an error.")
            elif price_distance_pips < 5:
                print(f"WARNING: Stop loss distance ({price_distance_pips:.1f} pips) is unusually small. This may be an error.")

            # Apply risk-reward ratio to calculate take profit
            if is_long:
                # For long trades, take profit is above entry price
                take_profit = entry_price + (price_distance * risk_reward_ratio)
                take_profit_distance_pips = (take_profit - entry_price) * 10000
                print(f"Long take profit calculation: {entry_price} + ({price_distance} * {risk_reward_ratio}) = {take_profit} ({take_profit_distance_pips:.1f} pips from entry)")
            else:
                # For short trades, take profit is below entry price
                take_profit = entry_price - (price_distance * risk_reward_ratio)
                take_profit_distance_pips = (entry_price - take_profit) * 10000
                print(f"Short take profit calculation: {entry_price} - ({price_distance} * {risk_reward_ratio}) = {take_profit} ({take_profit_distance_pips:.1f} pips from entry)")

            # Validate the take profit price
            if is_long and take_profit <= entry_price:
                print(f"WARNING: Calculated take profit ({take_profit}) is not above entry price ({entry_price}) for LONG trade. Using default 2% take profit.")
                take_profit = entry_price * 1.02
            elif not is_long and take_profit >= entry_price:
                print(f"WARNING: Calculated take profit ({take_profit}) is not below entry price ({entry_price}) for SHORT trade. Using default 2% take profit.")
                take_profit = entry_price * 0.98

            return take_profit

        except Exception as e:
            print(f"Error calculating take profit: {e}")
            print(f"Using default take profit (2% from entry)")

            # Default to 2% from entry price
            if is_long:
                return entry_price * 1.02  # 2% above entry for longs
            else:
                return entry_price * 0.98  # 2% below entry for shorts

    def _check_position_exit(self,
                           index: int,
                           strategy: Strategy,
                           position: Position) -> Tuple[bool, Optional[str], Optional[str]]:
        """Check if current position should be closed. Returns (should_exit, exit_rule, exit_reason)"""
        current_price = self.data['close'].iloc[index]

        # Check stop loss and take profit
        if position.stop_loss:
            if (position.trade_type == TradeType.LONG and
                current_price <= position.stop_loss):
                return True, None, "stop_loss"
            if (position.trade_type == TradeType.SHORT and
                current_price >= position.stop_loss):
                return True, None, "stop_loss"

        if position.take_profit:
            if (position.trade_type == TradeType.LONG and
                current_price >= position.take_profit):
                return True, None, "take_profit"
            if (position.trade_type == TradeType.SHORT and
                current_price <= position.take_profit):
                return True, None, "take_profit"

        # Check exit rules
        exit_rules = [
            rule for rule in strategy.exit_rules
            if rule.trade_type == position.trade_type
        ]
        group_operator = (strategy.exit_buy_group_operator
                         if position.trade_type == TradeType.LONG
                         else strategy.exit_sell_group_operator)

        if self._evaluate_rules(exit_rules, group_operator, index):
            # Find which rule triggered (for now, just use the first matching rule)
            for rule in exit_rules:
                if self._evaluate_condition(rule, index, index-1):
                    return True, str(rule), "rule"
            return True, None, "rule"
        return False, None, None

    def _calculate_position_size(self,
                               price: float,
                               strategy: Strategy,
                               index: int,
                               is_long: bool = True) -> float:
        """
        Calculate position size based on risk management rules.

        Args:
            price: Current price
            strategy: Strategy configuration
            index: Current index in the data
            is_long: True for long trades, False for short trades

        Returns:
            float: Position size
        """
        # Get risk percentage (default to 1% if not specified)
        risk_percentage = float(strategy.risk_management.risk_percentage or 1) / 100
        risk_amount = self.current_capital * risk_percentage
        print(f"Risk amount: ${risk_amount:.2f} ({risk_percentage*100:.2f}% of ${self.current_capital:.2f})")

        # Handle different stop loss methods
        # Force the stop_loss_method to be a StopLossMethod enum if it's a string
        stop_loss_method = strategy.risk_management.stop_loss_method
        if isinstance(stop_loss_method, str):
            try:
                stop_loss_method = StopLossMethod(stop_loss_method)
                print(f"Converted string stop_loss_method '{strategy.risk_management.stop_loss_method}' to enum: {stop_loss_method}")
            except ValueError as e:
                print(f"Error converting stop_loss_method: {e}")

        if stop_loss_method == StopLossMethod.FIXED:
            # Fixed pips stop loss
            if strategy.risk_management.fixed_pips:
                try:
                    # Log the fixed_pips value for debugging
                    print(f"Fixed pips value for position sizing: '{strategy.risk_management.fixed_pips}'")

                    # Convert to float, handling potential string formatting issues
                    fixed_pips_str = str(strategy.risk_management.fixed_pips).strip()
                    pips = float(fixed_pips_str)

                    # Validate the pips value
                    if pips <= 0:
                        print(f"Warning: Invalid fixed pips value: {pips}. Using default 20 pips for position sizing.")
                        pips = 20.0
                    elif pips > 500:
                        print(f"Warning: Fixed pips value {pips} is unusually large. Capping at 500 pips for position sizing.")
                        pips = 500.0

                    pip_value = 0.0001  # For forex
                    price_risk = pips * pip_value

                    # Verify the price risk is correct
                    actual_pips = price_risk * 10000
                    print(f"Position sizing with fixed pips: {pips} pips = {price_risk:.6f} price risk (actual: {actual_pips:.1f} pips)")

                    # Ensure we're using exactly the requested pips value
                    if abs(actual_pips - pips) > 1:  # Allow for small floating point differences
                        print(f"WARNING: Price risk ({actual_pips:.1f} pips) doesn't match requested pips ({pips}). Recalculating...")
                        price_risk = pips * 0.0001  # Force exact calculation
                        print(f"Recalculated price risk: {price_risk:.6f} ({pips} pips)")

                    if price_risk > 0:
                        size = risk_amount / price_risk
                        print(f"Position size calculation: {risk_amount:.2f} / {price_risk:.6f} = {size:.2f}")
                    else:
                        size = risk_amount / (price * 0.02)  # Default 2% if no valid risk
                        print(f"Using default 2% price risk for position sizing: {size:.2f}")
                except (ValueError, TypeError) as e:
                    print(f"Error parsing fixed pips value for position sizing: {e}")
                    print("Using default 20 pips for position sizing")
                    pips = 20.0
                    pip_value = 0.0001
                    price_risk = pips * pip_value

                    # Verify the price risk is correct
                    actual_pips = price_risk * 10000
                    print(f"Default position sizing with fixed pips: {pips} pips = {price_risk:.6f} price risk (actual: {actual_pips:.1f} pips)")

                    size = risk_amount / price_risk
                    print(f"Position size with default pips: {size:.2f}")

            # Backward compatibility with old stop_loss field
            elif strategy.risk_management.stop_loss:
                stop_loss_price = self._calculate_stop_loss_legacy(price, strategy.risk_management.stop_loss, is_long)
                price_risk = abs(price - stop_loss_price)
                print(f"Position sizing with legacy stop loss: {strategy.risk_management.stop_loss}, price risk: {price_risk:.6f}")

                if price_risk > 0:
                    size = risk_amount / price_risk
                    print(f"Position size calculation: {risk_amount:.2f} / {price_risk:.6f} = {size:.2f}")
                else:
                    size = risk_amount / (price * 0.02)  # Default 2% if no valid risk
                    print(f"Using default 2% price risk for position sizing: {size:.2f}")
            else:
                # Default to 2% price risk if no stop loss specified
                size = risk_amount / (price * 0.02)
                print(f"No stop loss specified. Using default 2% price risk for position sizing: {size:.2f}")

        elif stop_loss_method == StopLossMethod.RISK:
            # Risk-based position sizing (fixed lot size)
            if strategy.risk_management.lot_size:
                try:
                    lot_size_str = str(strategy.risk_management.lot_size).strip()
                    size = float(lot_size_str)
                    print(f"Using fixed lot size: {size}")
                except (ValueError, TypeError) as e:
                    print(f"Error parsing lot size value: {e}")
                    size = (self.current_capital * 0.01) / price
                    print(f"Using default position size: {size:.2f}")
            else:
                # Default to 1% of capital as position size
                size = (self.current_capital * 0.01) / price
                print(f"No lot size specified. Using default position size: {size:.2f}")

        elif stop_loss_method == StopLossMethod.INDICATOR:
            # Indicator-based stop loss
            try:
                stop_loss_price = self._calculate_indicator_stop_loss(index, price, strategy, is_long)
                price_risk = abs(price - stop_loss_price)
                print(f"Position sizing with indicator-based stop loss. Price risk: {price_risk:.6f}")

                if price_risk > 0:
                    size = risk_amount / price_risk
                    print(f"Position size calculation: {risk_amount:.2f} / {price_risk:.6f} = {size:.2f}")
                else:
                    size = risk_amount / (price * 0.02)  # Default 2% if no valid risk
                    print(f"Using default 2% price risk for position sizing: {size:.2f}")
            except Exception as e:
                print(f"Error calculating indicator-based position size: {e}")
                size = risk_amount / (price * 0.02)  # Default 2% if error
                print(f"Using default 2% price risk for position sizing due to error: {size:.2f}")
        else:
            # Default to 1% of capital as position size
            size = (self.current_capital * 0.01) / price
            print(f"Unknown stop loss method. Using default position size: {size:.2f}")

        # Ensure position size is reasonable (not too large or too small)
        max_size = self.current_capital / price * 0.5  # Max 50% of capital
        if size > max_size:
            print(f"Position size {size:.2f} exceeds maximum allowed {max_size:.2f}. Limiting size.")
            size = max_size

        return size

    def _update_metrics(self, position: Position) -> None:
        """Update performance metrics after position close."""
        self.total_trades += 1
        self.total_pnl += position.pnl
        self.total_costs += position.costs
        self.net_pnl += position.net_pnl

        # Update capital with net PnL
        self.current_capital += position.net_pnl
        self.equity_curve.append(self.current_capital)

        # Calculate return
        period_return = position.net_pnl / self.current_capital
        self.returns.append(period_return)

        if position.net_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1

        # Update max drawdown
        if len(self.equity_curve) > 1:
            peak = max(self.equity_curve[:-1])
            if self.current_capital < peak:
                drawdown = (peak - self.current_capital) / peak
                self.max_drawdown = max(self.max_drawdown, drawdown)

    def run(self, strategy: Strategy) -> Dict:
        """
        Run backtest with given strategy.

        Args:
            strategy: Strategy configuration

        Returns:
            dict: Backtest results and performance metrics
        """
        try:
            # Log strategy risk management settings
            print(f"\nStrategy risk management settings:")
            print(f"  Stop loss method: {strategy.risk_management.stop_loss_method}")
            print(f"  Risk percentage: {strategy.risk_management.risk_percentage}")
            print(f"  Risk-reward ratio: {strategy.risk_management.risk_reward_ratio} (type: {type(strategy.risk_management.risk_reward_ratio)})")
            print(f"  Legacy stop loss: {strategy.risk_management.stop_loss}")
            print(f"  Legacy take profit: {strategy.risk_management.take_profit}")

            # Log method-specific settings
            if strategy.risk_management.fixed_pips:
                print(f"  Fixed pips: {strategy.risk_management.fixed_pips}")

            if strategy.risk_management.lot_size:
                print(f"  Lot size: {strategy.risk_management.lot_size}")

            if strategy.risk_management.indicator_based_sl:
                print(f"  Indicator-based stop loss: {strategy.risk_management.indicator_based_sl.indicator}")
                print(f"  Indicator parameters: {strategy.risk_management.indicator_based_sl.parameters}")

            # Initialize variables
            self.current_capital = self.initial_capital
            self.trades = []
            self.equity_curve = []
            current_position = None

            # Track performance metrics
            winning_trades = 0
            losing_trades = 0
            total_profit = 0
            total_loss = 0
            max_drawdown = 0
            peak_capital = self.initial_capital

            # Setup indicators
            self._setup_indicators(strategy)

            # Store trading costs in instance variables for use in calculations
            self.spread_pips = strategy.trading_costs.spread_pips
            self.commission_percentage = strategy.trading_costs.commission_percentage
            self.commission_fixed = strategy.trading_costs.commission_fixed

            # Determine warm-up period for indicators
            warm_up_period = 0
            for indicator in self.indicators.values():
                if hasattr(indicator, 'period'):
                    warm_up_period = max(warm_up_period, indicator.period)

            # Add some extra periods for safety
            warm_up_period += 10

            print(f"Using warm-up period of {warm_up_period} bars")

            # Pre-fill equity curve for warm-up period
            for _ in range(warm_up_period):
                self.equity_curve.append(self.current_capital)

            # Process each bar, starting after warm-up period
            for i in range(warm_up_period, len(self.data)):
                timestamp = self.data.index[i].timestamp()  # Get Unix timestamp from datetime
                current_price = self.data.iloc[i]

                # Update equity curve
                self.equity_curve.append(self.current_capital)

                # Check for entry signals if no position
                if current_position is None:
                    # Check entry conditions
                    entry_signal = self._check_entry_conditions(strategy, i)
                    if entry_signal:
                        # Get trade type
                        is_long = entry_signal['type'] == TradeType.LONG

                        # Calculate position size
                        size = self._calculate_position_size(
                            current_price['close'],
                            strategy,
                            i,
                            is_long
                        )

                        # Calculate stop loss and take profit prices
                        stop_loss = self._calculate_stop_loss(
                            i,
                            current_price['close'],
                            strategy,
                            is_long
                        )

                        take_profit = self._calculate_take_profit(
                            i,
                            current_price['close'],
                            stop_loss,
                            strategy,
                            is_long
                        )

                        # Calculate stop loss distance in pips for logging
                        stop_loss_distance_pips = abs(current_price['close'] - stop_loss) * 10000
                        print(f"Opening position with stop loss at {stop_loss} ({stop_loss_distance_pips:.1f} pips from entry)")

                        # Verify the stop loss distance is reasonable and matches the strategy settings
                        # Force the stop_loss_method to be a StopLossMethod enum if it's a string
                        stop_loss_method = strategy.risk_management.stop_loss_method
                        if isinstance(stop_loss_method, str):
                            try:
                                stop_loss_method = StopLossMethod(stop_loss_method)
                                print(f"Converted string stop_loss_method '{strategy.risk_management.stop_loss_method}' to enum: {stop_loss_method}")
                            except ValueError as e:
                                print(f"Error converting stop_loss_method: {e}")

                        if stop_loss_method == StopLossMethod.FIXED and strategy.risk_management.fixed_pips:
                            try:
                                expected_pips = float(str(strategy.risk_management.fixed_pips).strip())
                                if abs(stop_loss_distance_pips - expected_pips) > 5:  # Allow for small differences
                                    print(f"WARNING: Actual stop loss distance ({stop_loss_distance_pips:.1f} pips) doesn't match expected fixed pips ({expected_pips})")
                                    # Recalculate the stop loss to ensure it's correct
                                    pip_value = 0.0001
                                    if entry_signal['type'] == TradeType.LONG:
                                        stop_loss = current_price['close'] - (expected_pips * pip_value)
                                    else:
                                        stop_loss = current_price['close'] + (expected_pips * pip_value)

                                    # Verify the recalculated stop loss
                                    new_distance_pips = abs(current_price['close'] - stop_loss) * 10000
                                    print(f"Recalculated stop loss: {stop_loss} ({new_distance_pips:.1f} pips from entry)")
                            except (ValueError, TypeError) as e:
                                print(f"Error verifying fixed pips: {e}")

                        elif stop_loss_method == StopLossMethod.RISK and strategy.risk_management.lot_size and strategy.risk_management.risk_percentage:
                            try:
                                # Calculate expected pips for risk-based stop loss
                                lot_size = float(str(strategy.risk_management.lot_size).strip())
                                risk_percentage = float(str(strategy.risk_management.risk_percentage).strip()) / 100
                                risk_amount = self.current_capital * risk_percentage
                                pip_monetary_value = lot_size * 10  # $10 per pip per standard lot

                                if pip_monetary_value > 0:
                                    expected_pips = risk_amount / pip_monetary_value

                                    # Cap at reasonable values
                                    if expected_pips <= 0:
                                        expected_pips = 20.0
                                    elif expected_pips > 500:
                                        expected_pips = 500.0

                                    if abs(stop_loss_distance_pips - expected_pips) > 5:  # Allow for small differences
                                        print(f"WARNING: Actual stop loss distance ({stop_loss_distance_pips:.1f} pips) doesn't match expected risk-based pips ({expected_pips:.1f})")
                                        # Recalculate the stop loss to ensure it's correct
                                        pip_value = 0.0001
                                        if entry_signal['type'] == TradeType.LONG:
                                            stop_loss = current_price['close'] - (expected_pips * pip_value)
                                        else:
                                            stop_loss = current_price['close'] + (expected_pips * pip_value)

                                        # Verify the recalculated stop loss
                                        new_distance_pips = abs(current_price['close'] - stop_loss) * 10000
                                        print(f"Recalculated risk-based stop loss: {stop_loss} ({new_distance_pips:.1f} pips from entry)")
                            except (ValueError, TypeError, ZeroDivisionError) as e:
                                print(f"Error verifying risk-based stop loss: {e}")

                        # Open new position
                        current_position = {
                            'type': entry_signal['type'],
                            'entry_price': current_price['close'],
                            'entry_time': int(timestamp),
                            'size': size,
                            'stop_loss': stop_loss,
                            'take_profit': take_profit,
                            'entry_rule': entry_signal.get('rule')
                        }

                        # Record trade
                        self.trades.append({
                            'trade_type': entry_signal['type'],
                            'entry_time': int(timestamp),
                            'entry_price': current_price['close'],
                            'size': size,
                            'stop_loss': stop_loss,
                            'stop_loss_pips': stop_loss_distance_pips,  # Add pips distance for reference
                            'take_profit': take_profit,
                            'entry_rule': entry_signal.get('rule')
                        })

                # Check for exit signals if in position
                elif current_position:
                    # Check exit conditions
                    exit_signal = self._check_exit_conditions(strategy, i, current_position)
                    if exit_signal:
                        # Use the exit price from the signal (which might be stop loss or take profit price)
                        exit_price = exit_signal.get('exit_price', current_price['close'])

                        # Calculate PnL
                        gross_pnl = self._calculate_pnl(current_position, exit_price)

                        # Calculate trading costs using instance variables
                        spread_cost = self.spread_pips * 0.0001 * current_position['size']
                        commission = (self.commission_percentage / 100) * abs(gross_pnl)
                        total_costs = spread_cost + commission + self.commission_fixed

                        # Calculate net PnL
                        net_pnl = gross_pnl - total_costs

                        # Update trade record
                        trade = self.trades[-1]
                        trade.update({
                            'exit_time': int(timestamp),
                            'exit_price': exit_price,
                            'gross_pnl': gross_pnl,
                            'costs': total_costs,
                            'net_pnl': net_pnl,
                            'exit_rule': exit_signal.get('rule'),
                            'exit_reason': exit_signal['reason']
                        })

                        # Update metrics based on actual PnL
                        if net_pnl > 0:
                            winning_trades += 1
                            total_profit += gross_pnl
                        else:
                            losing_trades += 1
                            total_loss += abs(gross_pnl)

                        # Update capital
                        self.current_capital += net_pnl

                        # Update max drawdown
                        if self.current_capital > peak_capital:
                            peak_capital = self.current_capital
                        drawdown = (peak_capital - self.current_capital) / peak_capital
                        max_drawdown = max(max_drawdown, drawdown)

                        # Reset position
                        current_position = None

            # Calculate final metrics
            total_trades = len(self.trades)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Return results
            return {
                'trades': self.trades,
                'equity_curve': self.equity_curve,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'gross_profit': total_profit,
                'gross_loss': total_loss,
                'net_pnl': total_profit - total_loss,
                'max_drawdown': max_drawdown,
                'final_value': self.current_capital,
                'total_return': (self.current_capital - self.initial_capital) / self.initial_capital,
                'sharpe_ratio': self._calculate_sharpe_ratio()
            }

        except Exception as e:
            print(f"Error in backtest engine: {str(e)}")
            raise

    def _check_entry_conditions(self, strategy: Strategy, index: int) -> Optional[dict]:
        """Check entry conditions for both long and short positions."""
        print(f"\nChecking entry conditions at index {index}, timestamp: {self.data.index[index]}")
        print(f"Current price: {self.data.iloc[index]['close']}")

        # Check long entry conditions
        long_rules = [rule for rule in strategy.entry_rules if rule.trade_type == TradeType.LONG]
        print(f"Found {len(long_rules)} long entry rules")

        if long_rules:
            print(f"Evaluating long rules with {strategy.entry_buy_group_operator} operator")
            if self._evaluate_rules(long_rules, strategy.entry_buy_group_operator, index):
                # Find which rule triggered
                triggered_rule = None
                for rule in long_rules:
                    if self._evaluate_condition(rule, index, index-1):
                        triggered_rule = str(rule)
                        print(f"Long entry triggered by rule: {triggered_rule}")
                        break
                return {
                    'type': TradeType.LONG,
                    'rule': triggered_rule
                }
            else:
                print("No long entry signal")

        # Check short entry conditions
        short_rules = [rule for rule in strategy.entry_rules if rule.trade_type == TradeType.SHORT]
        print(f"Found {len(short_rules)} short entry rules")

        if short_rules:
            print(f"Evaluating short rules with {strategy.entry_sell_group_operator} operator")
            if self._evaluate_rules(short_rules, strategy.entry_sell_group_operator, index):
                # Find which rule triggered
                triggered_rule = None
                for rule in short_rules:
                    if self._evaluate_condition(rule, index, index-1):
                        triggered_rule = str(rule)
                        print(f"Short entry triggered by rule: {triggered_rule}")
                        break
                return {
                    'type': TradeType.SHORT,
                    'rule': triggered_rule
                }
            else:
                print("No short entry signal")

        return None

    def _check_exit_conditions(self, strategy: Strategy, index: int, position: dict) -> Optional[dict]:
        """Check exit conditions for the current position."""
        current_price = self.data.iloc[index]['close']
        entry_price = position['entry_price']
        is_long = position['type'] == TradeType.LONG

        print(f"\nChecking exit conditions for {'LONG' if is_long else 'SHORT'} trade:")
        print(f"Entry Price: {entry_price}")
        print(f"Current Price: {current_price}")

        # For shorts: profit when price goes down, loss when price goes up
        # For longs: profit when price goes up, loss when price goes down
        price_change = current_price - entry_price
        price_change_pips = price_change * 10000  # Convert to pips

        print(f"Price change: {price_change_pips:.1f} pips ({'profit' if (price_change_pips > 0) == is_long else 'loss'})")

        # Get stop loss price from the position - this is set at entry time
        stop_loss = position.get('stop_loss')

        if stop_loss is None:
            # If for some reason the position doesn't have a stop loss, calculate it
            stop_loss = self._calculate_stop_loss(index, entry_price, strategy, is_long)
            print(f"Warning: Position had no stop loss, calculated: {stop_loss}")

        # Calculate the distance in pips from entry to stop loss
        stop_loss_distance_pips = abs(entry_price - stop_loss) * 10000
        print(f"Stop Loss price: {stop_loss} ({'below' if is_long else 'above'} entry, {stop_loss_distance_pips:.1f} pips)")

        # Check if stop loss is hit - use low/high prices for more accurate stop loss checking
        current_low = self.data.iloc[index]['low']
        current_high = self.data.iloc[index]['high']

        if is_long:
            # Long stop loss: trigger when price falls below stop loss (check low price)
            if current_low <= stop_loss:
                print(f"Long Stop Loss triggered: Low price {current_low} <= {stop_loss}")
                return {
                    'reason': 'stop_loss',
                    'exit_price': stop_loss
                }
        else:
            # Short stop loss: trigger when price rises above stop loss (check high price)
            if current_high >= stop_loss:
                print(f"Short Stop Loss triggered: High price {current_high} >= {stop_loss}")
                return {
                    'reason': 'stop_loss',
                    'exit_price': stop_loss
                }

        # Get take profit price - either from position or calculate it
        take_profit = None

        # If position already has a take profit, use it
        if 'take_profit' in position and position['take_profit'] is not None:
            take_profit = position['take_profit']
        # Otherwise, calculate it based on strategy risk management
        else:
            take_profit = self._calculate_take_profit(index, entry_price, stop_loss, strategy, is_long)

        print(f"Take Profit price: {take_profit} ({'above' if is_long else 'below'} entry)")

        # Check if take profit is hit - use high/low prices for more accurate take profit checking
        if is_long:
            # Long take profit: trigger when price rises above take profit (check high price)
            if current_high >= take_profit:
                print(f"Long Take Profit triggered: High price {current_high} >= {take_profit}")
                return {
                    'reason': 'take_profit',
                    'exit_price': take_profit
                }
        else:
            # Short take profit: trigger when price falls below take profit (check low price)
            if current_low <= take_profit:
                print(f"Short Take Profit triggered: Low price {current_low} <= {take_profit}")
                return {
                    'reason': 'take_profit',
                    'exit_price': take_profit
                }

        # Check exit rules
        exit_rules = [rule for rule in strategy.exit_rules if rule.trade_type == position['type']]
        group_operator = (strategy.exit_buy_group_operator
                         if is_long
                         else strategy.exit_sell_group_operator)

        if self._evaluate_rules(exit_rules, group_operator, index):
            # Find which rule triggered
            triggered_rule = None
            for rule in exit_rules:
                if self._evaluate_condition(rule, index, index-1):
                    triggered_rule = str(rule)
                    break
            print(f"Exit signal triggered by rule: {triggered_rule}")
            return {
                'reason': 'signal',
                'rule': triggered_rule,
                'exit_price': current_price
            }

        return None

    def _calculate_pnl(self, position: dict, exit_price: float) -> float:
        """Calculate the PnL for a position."""
        size = position['size']
        entry_price = position['entry_price']
        is_long = position['type'] == TradeType.LONG

        # Calculate PnL
        if is_long:
            pnl = (exit_price - entry_price) * size
        else:  # SHORT
            pnl = (entry_price - exit_price) * size

        print(f"PnL Calculation:")
        print(f"{'LONG' if is_long else 'SHORT'} trade:")
        print(f"Entry: {entry_price}, Exit: {exit_price}")
        print(f"Size: {size}")
        print(f"PnL: {pnl}")

        return pnl

    def _calculate_sharpe_ratio(self) -> float:
        """Calculate the Sharpe ratio of the strategy."""
        if len(self.equity_curve) < 2:
            return 0.0

        # Calculate daily returns
        returns = np.diff(self.equity_curve) / self.equity_curve[:-1]

        # Annualize metrics (assuming daily data)
        annual_return = np.mean(returns) * 252
        annual_volatility = np.std(returns) * np.sqrt(252)

        # Calculate Sharpe ratio (assuming risk-free rate of 0)
        if annual_volatility == 0:
            return 0.0
        return annual_return / annual_volatility

    # ... rest of the class implementation ...