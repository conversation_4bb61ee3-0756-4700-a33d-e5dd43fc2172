from firebase_functions import https_fn, firestore_fn
import firebase_admin
from firebase_admin import initialize_app, firestore, auth, storage
from flask import jsonify
from pydantic import BaseModel, ValidationError
import requests
import json
import os
from dotenv import load_dotenv
os.environ['MPLBACKEND'] = 'Agg'
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import io
import base64
import backtrader as bt
import pandas as pd
import numpy as np
import datetime
import time
import random
import copy
import traceback
import json
from algebra.mathematical_calculations import MathematicalCalculations
from backtrader_indicators import INDICATOR_MAPPING
import functions_framework
from firebase_functions import https_fn
import firebase_admin
from firebase_admin import firestore, initialize_app
from google.cloud import storage, pubsub_v1
from firebase_functions.firestore_fn import on_document_created, Event, DocumentSnapshot
import openai
from openai import OpenAI
from google.cloud import storage
from config import OPENAI_API_KEY, OANDA_API_URL, BUCKET_NAME, CSV_FILENAME, LOCAL_FOREX_DATA_DIR, DEFAULT_SYMBOL, DEFAULT_TIMEFRAME
import pathlib
from equity_analyzer import EquityAnalyzer
from datetime import datetime, timedelta, timezone
import sendgrid
from sendgrid.helpers.mail import Mail
from sendgrid import SendGridAPIClient
import logging
from google.api_core import exceptions

# Load environment variables from .env file if it exists
if os.path.exists('.env'):
    load_dotenv()

# Initialize OpenAI with API key from config
openai.api_key = OPENAI_API_KEY
if not openai.api_key:
    print("openai.api_key", openai.api_key)
    raise ValueError("OPENAI_API_KEY is not set in config.py")

# Initialize Firebase Admin SDK (if not already initialized)
if not firebase_admin._apps:
    initialize_app()

db = firestore.client()

# Set emulator host only in development
if os.getenv('FUNCTIONS_EMULATOR'):
    os.environ["FIRESTORE_EMULATOR_HOST"] = "127.0.0.1:8080"
    print("Running in emulator mode")

# Get project ID from environment or default to development
PROJECT_ID = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
TOPIC_NAME = "strategy-execution"
BUCKET_NAME = os.getenv('BACKTEST_BUCKET_NAME', 'your-backtest-data-bucket')
CSV_FILENAME = "eur_usd_hourly.csv"

# OANDA API configuration
OANDA_API_URL = os.getenv('OANDA_API_URL', 'https://api-fxpractice.oanda.com/v3')

# Polygon API configuration
POLYGON_API_KEY = os.getenv('POLYGON_API_KEY')
POLYGON_BASE_URL = "https://api.polygon.io/v2"

class SignupRequest(BaseModel):
    email: str
    firebase_uid: str

class APIKeyRequest(BaseModel):
    firebase_uid: str
    api_key: str

class SaveStrategyRequest(BaseModel):
    firebase_uid: str
    strategy: dict

class UpdateStrategyRequest(BaseModel):
    firebase_uid: str
    strategyId: str  # document ID in Firestore (string)
    strategy: dict

db = firestore.client()

def is_emulator():
    """Check if we're running in the Firebase emulator."""
    return os.environ.get("FUNCTIONS_EMULATOR") == "true" or os.environ.get("FIRESTORE_EMULATOR_HOST") is not None

def load_forex_data_from_csv():
    """Load forex data from CSV file, with fallback options for different environments."""
    print("🔄 Loading forex data for backtesting...")

    # In emulator mode, try to load from local files first
    if is_emulator():
        print("🔧 Running in emulator mode, trying local files...")
        try:
            # Look for the local file in the forex_data directory
            local_data_dir = pathlib.Path(LOCAL_FOREX_DATA_DIR)

            # First check if the directory exists
            if not local_data_dir.exists():
                print(f"⚠️ Local data directory {LOCAL_FOREX_DATA_DIR} not found")
                raise FileNotFoundError(f"Directory {LOCAL_FOREX_DATA_DIR} not found")

            # Check for CSV files in the directory
            file_pattern = f"{DEFAULT_SYMBOL}_{DEFAULT_TIMEFRAME}.csv"
            matching_files = list(local_data_dir.glob(file_pattern))

            if not matching_files:
                # Try a more generic search if specific pattern not found
                matching_files = list(local_data_dir.glob("*.csv"))

            if matching_files:
                # Use the first matching file
                csv_path = "forex_data/eur_usd_hourly.csv"
                print(f"📂 Loading data from local file: {csv_path}")

                # Read the CSV file without headers and with tab separator
                df = pd.read_csv(csv_path,
                               sep='\t',  # Tab separator
                               header=None,  # No header row
                               names=['datetime', 'open', 'high', 'low', 'close', 'volume'])  # Define column names

                print(f"📊 Loaded {len(df)} rows of data")

                # Process datetime and set index
                df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')
                df = df.dropna(subset=['datetime'])
                df.set_index('datetime', inplace=True)
                df = df.sort_index()

                # Ensure all required columns are numeric
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

                # Drop any rows with missing values
                df = df.dropna(subset=['open', 'high', 'low', 'close'])

                print(f"✅ Successfully processed DataFrame with {len(df)} rows")
                print(f"📅 Data range: {df.index.min()} to {df.index.max()}")
                return df
            else:
                print(f"⚠️ No CSV files found in {LOCAL_FOREX_DATA_DIR}")
                raise FileNotFoundError(f"No CSV files found in {LOCAL_FOREX_DATA_DIR}")

        except Exception as e:
            print(f"⚠️ Error loading local file: {str(e)}")
            print("Falling back to synthetic data...")
    else:
        # In production mode, try to load from Google Cloud Storage
        try:
            print("☁️ Attempting to load data from Google Cloud Storage...")
            client = storage.Client()
            bucket = client.bucket(BUCKET_NAME)
            blob = bucket.blob(CSV_FILENAME)
            csv_data = blob.download_as_text()
            df = pd.read_csv(io.StringIO(csv_data),
                           sep='\t',  # Tab separator
                           header=None,  # No header row
                           names=['datetime', 'open', 'high', 'low', 'close', 'volume'])  # Define column names
            print(f"✅ Loaded {len(df)} rows of Forex data from Cloud Storage")

            # Process DataFrame
            df['datetime'] = pd.to_datetime(df['datetime'], errors='coerce')
            df = df.dropna(subset=['datetime'])
            df.set_index('datetime', inplace=True)
            df = df.sort_index()

            # Ensure all required columns are numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # Drop any rows with missing values
            df = df.dropna(subset=['open', 'high', 'low', 'close'])

            print(f"✅ Processed DataFrame with {len(df)} rows")
            print(f"📅 Data range: {df.index.min()} to {df.index.max()}")
            return df
        except Exception as e:
            print(f"⚠️ Cloud Storage error: {str(e)}")
            print("Falling back to synthetic data...")

    # Final fallback: Generate synthetic data
    print("🔄 Generating synthetic data for testing...")
    dates = pd.date_range(start='2023-01-01', periods=1000, freq='h')
    price = 1.1  # Starting price
    prices = []

    # Generate random walk prices
    for i in range(1000):
        price += np.random.normal(0, 0.001)  # Small random price changes
        prices.append(price)

    # Create DataFrame with synthetic data
    df = pd.DataFrame({
        'datetime': dates,
        'open': prices,
        'high': [p * (1 + np.random.uniform(0, 0.002)) for p in prices],
        'low': [p * (1 - np.random.uniform(0, 0.002)) for p in prices],
        'close': [p * (1 + np.random.normal(0, 0.0005)) for p in prices],
        'volume': np.random.randint(100, 1000, size=1000)
    })

    df.set_index('datetime', inplace=True)
    print(f"✅ Generated {len(df)} rows of synthetic Forex data for testing")
    print(f"📅 Data range: {df.index.min()} to {df.index.max()}")
    return df

def generateId():
    return str(int(time.time() * 1000)) + ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))

# -----------------------------
# Firebase Function: update_strategy
# -----------------------------
@https_fn.on_request()
def update_strategy(req: https_fn.Request) -> https_fn.Response:
    # Define CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "PUT, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Content-Type": "application/json"
    }

    # Handle preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            status=204,
            headers=headers
        )

    print("Received update request body:", req.get_json())

    try:
        # Parse request data
        request_data = UpdateStrategyRequest(**req.get_json())
        strategy_data = request_data.strategy

        # Log the risk management section specifically
        if 'riskManagement' in strategy_data:
            print(f"Risk Management data (update): {json.dumps(strategy_data['riskManagement'], indent=2)}")
            if 'indicatorBasedSL' in strategy_data['riskManagement']:
                print(f"Indicator-based SL (update): {json.dumps(strategy_data['riskManagement']['indicatorBasedSL'], indent=2)}")

                # Check if this is an ATR-based stop loss with empty parameters
                if (strategy_data['riskManagement']['indicatorBasedSL']['indicator'] == 'atr' and
                    (not strategy_data['riskManagement']['indicatorBasedSL']['parameters'] or
                     len(strategy_data['riskManagement']['indicatorBasedSL']['parameters']) == 0)):

                    print("ATR-based stop loss detected with empty parameters (update). Attempting to find ATR indicator parameters...")

                    # Look for ATR indicator in the indicators array
                    atr_indicator = next((ind for ind in strategy_data.get('indicators', [])
                                         if ind.get('type') == 'ATR' or ind.get('indicator_class') == 'ATR'), None)

                    if atr_indicator and 'parameters' in atr_indicator and atr_indicator['parameters']:
                        print(f"Found ATR indicator with parameters (update): {json.dumps(atr_indicator['parameters'], indent=2)}")

                        # Copy the parameters to the risk management section
                        strategy_data['riskManagement']['indicatorBasedSL']['parameters'] = atr_indicator['parameters'].copy()
                        print(f"Updated indicatorBasedSL parameters (update): {json.dumps(strategy_data['riskManagement']['indicatorBasedSL']['parameters'], indent=2)}")
                    else:
                        print("No ATR indicator found in the indicators array or it has no parameters (update)")

        # Ensure required fields are present
        required_fields = ['name', 'instruments', 'timeframe', 'tradingSession', 'indicators', 'entryRules', 'exitRules']
        missing_fields = [field for field in required_fields if field not in strategy_data]
        if missing_fields:
            return https_fn.Response(
                response=json.dumps({"message": f"Missing required fields: {', '.join(missing_fields)}"}),
                status=400,
                headers=headers
            )

        # Initialize Firestore client
        db = firestore.client()

        # Check if strategy exists
        strategy_ref = db.collection('users').document(request_data.firebase_uid).collection('strategies').document(request_data.strategyId)
        strategy_doc = strategy_ref.get()

        if not strategy_doc.exists:
            return https_fn.Response(
                response=json.dumps({"message": "Strategy not found"}),
                status=404,
                headers=headers
            )

        # Check if another strategy with the same name exists (excluding current strategy)
        strategies_ref = db.collection('users').document(request_data.firebase_uid).collection('strategies')
        existing_strategy = strategies_ref.where('name', '==', strategy_data['name']).get()

        for doc in existing_strategy:
            if doc.id != request_data.strategyId:
                return https_fn.Response(
                    response=json.dumps({"message": "Another strategy with this name already exists"}),
                    status=400,
                    headers=headers
                )

        # Prepare the document data
        document_data = {
            'name': strategy_data['name'],
            'description': strategy_data.get('description', ''),
            'updated_at': datetime.now().isoformat(),
            'strategy_json': json.dumps(strategy_data)  # Store the complete strategy as a JSON string
        }

        # Update strategy in Firestore
        strategy_ref.update(document_data)

        return https_fn.Response(
            response=json.dumps({"message": "Strategy updated successfully"}),
            headers=headers
        )

    except ValidationError as e:
        return https_fn.Response(
            response=json.dumps({"message": f"Invalid request format: {str(e)}"}),
            status=400,
            headers=headers
        )
    except Exception as e:
        print(f"Error updating strategy: {str(e)}")
        return https_fn.Response(
            response=json.dumps({"message": f"Failed to update strategy: {str(e)}"}),
            status=500,
            headers=headers
        )

# -----------------------------
# Firebase Function: save_strategy
# -----------------------------
@https_fn.on_request()
def save_strategy(req: https_fn.Request) -> https_fn.Response:
    # Define CORS headers
    headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Content-Type": "application/json"
    }

    # Handle preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            status=204,
            headers=headers
        )

    try:
        # Get request data and log it for debugging
        request_json = req.get_json()
        print(f"Received request data: {request_json}")

        # Parse request data
        request_data = SaveStrategyRequest(**request_json)
        strategy_data = request_data.strategy

        # Log the risk management section specifically
        if 'riskManagement' in strategy_data:
            print(f"Risk Management data: {json.dumps(strategy_data['riskManagement'], indent=2)}")
            if 'indicatorBasedSL' in strategy_data['riskManagement']:
                print(f"Indicator-based SL: {json.dumps(strategy_data['riskManagement']['indicatorBasedSL'], indent=2)}")

                # Check if this is an ATR-based stop loss with empty parameters
                if (strategy_data['riskManagement']['indicatorBasedSL']['indicator'] == 'atr' and
                    (not strategy_data['riskManagement']['indicatorBasedSL']['parameters'] or
                     len(strategy_data['riskManagement']['indicatorBasedSL']['parameters']) == 0)):

                    print("ATR-based stop loss detected with empty parameters. Attempting to find ATR indicator parameters...")

                    # Look for ATR indicator in the indicators array
                    atr_indicator = next((ind for ind in strategy_data.get('indicators', [])
                                         if ind.get('type') == 'ATR' or ind.get('indicator_class') == 'ATR'), None)

                    if atr_indicator and 'parameters' in atr_indicator and atr_indicator['parameters']:
                        print(f"Found ATR indicator with parameters: {json.dumps(atr_indicator['parameters'], indent=2)}")

                        # Copy the parameters to the risk management section
                        strategy_data['riskManagement']['indicatorBasedSL']['parameters'] = atr_indicator['parameters'].copy()
                        print(f"Updated indicatorBasedSL parameters: {json.dumps(strategy_data['riskManagement']['indicatorBasedSL']['parameters'], indent=2)}")
                    else:
                        print("No ATR indicator found in the indicators array or it has no parameters")

        # Ensure required fields are present
        required_fields = ['name', 'instruments', 'timeframe', 'tradingSession', 'indicators', 'entryRules', 'exitRules']
        missing_fields = [field for field in required_fields if field not in strategy_data]
        if missing_fields:
            error_msg = f"Missing required fields: {', '.join(missing_fields)}"
            print(f"Validation error: {error_msg}")
            return https_fn.Response(
                response=json.dumps({
                    "success": False,
                    "message": error_msg
                }),
                status=400,
                headers=headers
            )

        # Initialize Firestore client
        db = firestore.client()

        # Check if a strategy with the same name exists for this user
        strategies_ref = db.collection('users').document(request_data.firebase_uid).collection('strategies')
        existing_strategy = strategies_ref.where('name', '==', strategy_data['name']).get()

        if len(list(existing_strategy)) > 0:
            error_msg = "A strategy with this name already exists"
            print(f"Validation error: {error_msg}")
            return https_fn.Response(
                response=json.dumps({
                    "success": False,
                    "message": error_msg
                }),
                status=400,
                headers=headers
            )

        # Prepare the document data
        document_data = {
            'name': strategy_data['name'],
            'description': strategy_data.get('description', ''),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'strategy_json': json.dumps(strategy_data)  # Store the complete strategy as a JSON string
        }

        # Log the strategy JSON that will be saved to Firestore
        print(f"Strategy JSON to be saved: {document_data['strategy_json']}")

        # Parse the JSON string back to verify its contents
        parsed_json = json.loads(document_data['strategy_json'])
        if 'riskManagement' in parsed_json and 'indicatorBasedSL' in parsed_json['riskManagement']:
            print(f"Parsed indicatorBasedSL: {json.dumps(parsed_json['riskManagement']['indicatorBasedSL'], indent=2)}")

        # Save strategy to Firestore
        strategy_ref = strategies_ref.document()
        strategy_ref.set(document_data)

        # Return success response
        success_response = {
            "success": True,
            "message": "Strategy saved successfully",
            "data": {
                "strategyId": strategy_ref.id,
                "name": strategy_data['name']
            }
        }
        print(f"Strategy saved successfully: {success_response}")
        return https_fn.Response(
            response=json.dumps(success_response),
            headers=headers
        )

    except ValidationError as e:
        error_msg = f"Invalid request format: {str(e)}"
        print(f"Validation error: {error_msg}")
        return https_fn.Response(
            response=json.dumps({
                "success": False,
                "message": error_msg
            }),
            status=400,
            headers=headers
        )
    except Exception as e:
        error_msg = f"Failed to save strategy: {str(e)}"
        print(f"Error saving strategy: {error_msg}")
        return https_fn.Response(
            response=json.dumps({
                "success": False,
                "message": error_msg
            }),
            status=500,
            headers=headers
        )

# -----------------------------
# Firebase Function: get_strategies
# -----------------------------
@https_fn.on_request()
def get_strategies(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }
    if req.method == "OPTIONS":
        return https_fn.Response(json.dumps({"message": "Preflight CORS OK"}),
                                   status=204,
                                   mimetype="application/json",
                                   headers=cors_headers)
    if req.method != "GET":
        return https_fn.Response(json.dumps({"message": "Method Not Allowed"}),
                                   status=405,
                                   mimetype="application/json",
                                   headers=cors_headers)
    try:
        firebase_uid = req.args.get("firebase_uid")
        if not firebase_uid:
            return https_fn.Response(json.dumps({"message": "firebase_uid is required"}),
                                       status=400,
                                       mimetype="application/json",
                                       headers=cors_headers)
        strategies_ref = db.collection("users").document(firebase_uid).collection("strategies")
        docs = strategies_ref.stream()
        strategies_list = []
        for doc in docs:
            data = doc.to_dict()
            # Convert Firestore timestamp to ISO format string
            if 'created_time' in data:
                data['created_time'] = data['created_time'].isoformat()
            data["id"] = doc.id
            strategies_list.append(data)
        return https_fn.Response(json.dumps(strategies_list),
                                   status=200,
                                   mimetype="application/json",
                                   headers=cors_headers)
    except Exception as e:
        return https_fn.Response(json.dumps({"message": str(e)}),
                                   status=500,
                                   mimetype="application/json",
                                   headers=cors_headers)

# -----------------------------
# Backtesting Logic and Firebase Function: run_backtest
# -----------------------------
# Define a helper function to safely round values.
def safe_round(value, decimals=2):
    if value is None:
        return "N/A"
    return round(value, decimals)

# Define your dynamic strategy class (as in your original backtest service)
class DynamicStrategy(bt.Strategy):
    params = (
        ("indicators", []),
        ("entry_rules", []),
        ("exit_rules", []),
        ("stop_loss", None),
        ("take_profit", None),
        ("entryBuyGroupOperator", "AND"),
        ("entrySellGroupOperator", "AND"),
        ("exitBuyGroupOperator", "AND"),
        ("exitSellGroupOperator", "AND"),
    )
    MOVAV_MAPPING = {
        "SMA": bt.indicators.SMA,
        "EMA": bt.indicators.EMA,
        "WMA": bt.indicators.WMA,
        "HMA": bt.indicators.HullMovingAverage,
    }

    def __init__(self):
        # Initialize indicators dictionary
        self.indicators = {}
        self.trades = []  # List to track open trades
        self.warmup_period = 50  # Use a higher default warmup period (50 bars instead of 0)
        self.start_cash = self.broker.getvalue()  # Store starting cash for comparison
        self.daily_values = []  # Track daily portfolio values

        # Convert stop loss and take profit from percentage to decimal
        if self.p.stop_loss and isinstance(self.p.stop_loss, str):
            self.p.stop_loss = float(self.p.stop_loss.rstrip('%')) / 100
        if self.p.take_profit and isinstance(self.p.take_profit, str):
            self.p.take_profit = float(self.p.take_profit.rstrip('%')) / 100

        # Debugging info
        print(f"Initializing DynamicStrategy with {len(self.p.indicators)} indicators")
        print(f"Stop Loss: {self.p.stop_loss}, Take Profit: {self.p.take_profit}")

        # Add indicators based on the provided configuration
        for indicator in self.p.indicators:
            indicator_class = indicator.get("indicator_class")
            parameters = indicator.get("parameters", {})
            indicator_id = indicator["id"]

            print(f"Adding indicator: {indicator_class} with parameters {parameters}")

            # Calculate warmup period for each indicator
            period = parameters.get("period", 14)  # Default period is 14
            self.warmup_period = max(self.warmup_period, period)

            try:
                if indicator_class in self.MOVAV_MAPPING:
                    # Handle moving averages
                    self.indicators[indicator_id] = self.MOVAV_MAPPING[indicator_class](
                        self.data.close, **parameters
                    )
                elif indicator_class == "RSI":
                    # Handle RSI
                    self.indicators[indicator_id] = bt.indicators.RSI(
                        self.data.close, **parameters
                    )
                elif indicator_class == "MACD":
                    # Handle MACD
                    self.indicators[indicator_id] = bt.indicators.MACD(
                        self.data.close, **parameters
                    )
                elif indicator_class == "BollingerBands":
                    # Handle Bollinger Bands
                    self.indicators[indicator_id] = bt.indicators.BollingerBands(
                        self.data.close, **parameters
                    )
                elif indicator_class == "Stochastic":
                    # Handle Stochastic
                    self.indicators[indicator_id] = bt.indicators.Stochastic(
                        self.datas[0], **parameters
                    )
                elif indicator_class == "ADX":
                    # Handle ADX
                    self.indicators[indicator_id] = bt.indicators.DirectionalMovement(
                        self.datas[0], **parameters
                    )
                else:
                    print(f"Unsupported indicator class: {indicator_class}")
                    continue

                # Verify indicator was created properly
                print(f"Indicator {indicator_id} created: {self.indicators[indicator_id].__class__.__name__}")

            except Exception as e:
                print(f"Error creating indicator {indicator_class}: {str(e)}")
                traceback.print_exc()

    def build_rule_condition(self, rule):
        """Build a condition based on a rule."""
        try:
            indicator1 = rule.get("indicator1")
            operator = rule.get("operator")
            compare_type = rule.get("compareType")
            indicator2 = rule.get("indicator2")
            value = rule.get("value")

            # Get the current values for comparison
            left_value = self.get_safe_value(indicator1)

            # Set the right value based on compare type
            if compare_type == "indicator":
                right_value = self.get_safe_value(indicator2)
            else:
                try:
                    right_value = float(value)
                except (ValueError, TypeError):
                    print(f"Invalid value for comparison: {value}")
                    return False

            # Debug output
            print(f"Comparing: {left_value} {operator} {right_value}")

            # Simple comparisons
            if operator == ">":
                return left_value > right_value
            elif operator == "<":
                return left_value < right_value
            elif operator == "==":
                return abs(left_value - right_value) < 0.0001
            elif operator == "Crossing above" or operator == "Crossing below":
                # Simplified for now - just use basic comparison to get things working
                if operator == "Crossing above":
                    return left_value > right_value
                else:
                    return left_value < right_value
            else:
                print(f"Unknown operator: {operator}")
                return False
        except Exception as e:
            print(f"Error in build_rule_condition: {str(e)}")
            traceback.print_exc()
            return False

    def get_safe_value(self, indicator_id):
        """Safely extract a scalar value from any indicator or price."""
        try:
            if indicator_id == "price":
                return self.safe_extract_value(self.data.close[0])

            ind = self.indicators.get(indicator_id)
            if ind is None:
                print(f"Indicator {indicator_id} not found")
                return 0.0

            return self.safe_extract_value(ind[0])
        except Exception as e:
            print(f"Error getting value for {indicator_id}: {e}")
            traceback.print_exc()
            return 0.0

    def safe_extract_value(self, value_obj):
        """Extract a scalar float value from any type of object."""
        try:
            # Handle pandas Series
            if hasattr(value_obj, 'iloc') and hasattr(value_obj, 'values'):
                if len(value_obj) > 0:
                    return float(value_obj.iloc[0])
                else:
                    return 0.0

            # Handle numpy ndarray
            elif hasattr(value_obj, 'shape'):
                if hasattr(value_obj, 'item') and value_obj.size == 1:
                    return float(value_obj.item())
                elif len(value_obj) > 0:
                    return float(value_obj[0])
                else:
                    return 0.0

            # Handle Backtrader Line objects
            elif hasattr(value_obj, 'lines'):
                return float(value_obj.lines[0][0])

            # Direct conversion for scalars
            return float(value_obj)
        except Exception as e:
            print(f"Error extracting value: {e}, object type: {type(value_obj)}")
            print(f"Value representation: {repr(value_obj)}")
            # Last resort - try various formats
            try:
                if hasattr(value_obj, '__float__'):
                    return float(value_obj)
                elif hasattr(value_obj, '__index__'):
                    return float(value_obj.__index__())
                elif hasattr(value_obj, '__int__'):
                    return float(value_obj.__int__())
                elif hasattr(value_obj, '__len__') and len(value_obj) > 0:
                    return 1.0  # Return a non-zero sentinel value
                return 0.0
            except:
                return 0.0

    def evaluate_rule_group(self, rules, logical_operator="OR"):
        """Evaluate a group of rules with a logical operator."""
        if not rules:
            return False

        conditions = [self.build_rule_condition(rule) for rule in rules]

        if logical_operator == "AND":
            return all(conditions)
        else:  # OR
            return any(conditions)

    def get_indicator_values(self):
        """Get current values of all indicators."""
        values = {}
        for name, indicator in self.indicators.items():
            try:
                values[name] = indicator[0]
            except Exception as e:
                print(f"Error getting value for indicator {name}: {str(e)}")
                values[name] = None
        return values

    def stop(self):
        """Called when backtest ends - add final debugging information"""
        # Make sure we record the final value at the end of the backtest
        final_value = self.broker.getvalue()
        self.daily_values.append((len(self), final_value))

        print(f"Strategy execution finished")
        print(f"Starting cash: {self.start_cash:.2f}, Final cash: {final_value:.2f}")
        print(f"Difference: {final_value - self.start_cash:.2f}")
        print(f"Total trades executed: {len(self.trades)}")

        # Calculate trade statistics
        winning_trades = sum(1 for t in self.trades if 'exit_date' in t and t.get('pnl', 0) > 0)
        losing_trades = sum(1 for t in self.trades if 'exit_date' in t and t.get('pnl', 0) <= 0)
        closed_trades = winning_trades + losing_trades

        if closed_trades > 0:
            win_rate = (winning_trades / closed_trades) * 100
            print(f"Closed trades: {closed_trades}, Winning: {winning_trades}, Losing: {losing_trades}")
            print(f"Win rate: {win_rate:.2f}%")

        # Try to determine why value changed if no trades
        if len(self.trades) == 0 and abs(final_value - self.start_cash) > 0.01:
            print("WARNING: Portfolio value changed without trades!")
            print("This is likely due to how Backtrader handles unrealized gains on open positions")
            print("or to currency value changes in the forex feed.")

        # Print positions
        if self.broker.positions:
            print("Positions at backtest end:")
            for data, pos in self.broker.positions.items():
                print(f"  {data._name}: {pos} shares at {data.close[0]:.5f}")

        # Print equity curve information
        print(f"Equity curve data points: {len(self.daily_values)}")
        if len(self.daily_values) > 0:
            print(f"First value: {self.daily_values[0][1]:.2f}, Last value: {self.daily_values[-1][1]:.2f}")

    def next(self):
        """Called for each bar of data - check entry/exit conditions and place orders."""
        # Track value for debugging - record portfolio value every bar
        current_value = self.broker.getvalue()
        self.daily_values.append((len(self), current_value))

        # Skip the warmup period to allow indicators to stabilize
        if len(self) < self.warmup_period:
            return

        # Log current data point
        if (len(self) - self.warmup_period) % 20 == 0:  # Only log every 20 bars to reduce output
            print(f"Processing bar {len(self)}: Date={self.datetime.date()} Close={self.data.close[0]:.5f}")

        # Check if we have an open position
        position_size = self.getposition(self.data).size

        # First check entry conditions (only if we don't have a position)
        if position_size == 0:
            # Evaluate buy entry rules
            buy_entry_rules = [rule for rule in self.p.entry_rules if rule.get("tradeType") == "buy"]
            if buy_entry_rules:
                # Evaluate all buy rules
                buy_signals = [self.build_rule_condition(rule) for rule in buy_entry_rules]

                # Determine if we should buy based on logical operator
                should_buy = False
                if len(buy_signals) > 0:
                    if self.p.entryBuyGroupOperator == "OR":
                        should_buy = any(buy_signals)
                    else:  # AND
                        should_buy = all(buy_signals)

                # Avoid logging when not needed to reduce output
                if should_buy:
                    print(f"✅ BUY SIGNAL TRIGGERED at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")

                    # Calculate position size (risk 1% of account per trade)
                    # The stop loss will be either the specified percentage or default to 2%
                    stop_loss_pct = 0.02  # Default 2% stop loss
                    if self.p.stop_loss is not None:
                        if isinstance(self.p.stop_loss, str) and self.p.stop_loss.endswith('%'):
                            stop_loss_pct = float(self.p.stop_loss.rstrip('%')) / 100
                        else:
                            try:
                                stop_loss_pct = float(self.p.stop_loss)
                            except (ValueError, TypeError):
                                pass

                    # Risk 1% of account value
                    account_value = self.broker.getvalue()
                    risk_amount = account_value * 0.01  # 1% risk
                    price_risk = self.data.close[0] * stop_loss_pct  # Dollar amount of stop loss

                    # Calculate position size: risk amount divided by price risk
                    if price_risk > 0:
                        position_size = risk_amount / price_risk
                    else:
                        position_size = risk_amount / (self.data.close[0] * 0.02)  # Default 2% if issue

                    # Limit position size to maximum 10% of account
                    max_position_value = account_value * 0.10  # 10% of account value
                    max_position_size = max_position_value / self.data.close[0]
                    position_size = min(position_size, max_position_size)

                    print(f"Calculated position size: {position_size:.2f} units")
                    print(f"Account value: ${account_value:.2f}, Risk amount: ${risk_amount:.2f}")
                    print(f"Stop loss percent: {stop_loss_pct*100:.1f}%, Stop loss price: {self.data.close[0]*(1-stop_loss_pct):.5f}")

                    # Place buy order
                    self.buy(size=position_size)

                    # Record trade
                    self.trades.append({
                        'type': 'buy',
                        'entry_date': self.datetime.date(),
                        'entry_price': self.data.close[0],
                        'size': position_size,
                        'stop_loss': self.data.close[0] * (1 - stop_loss_pct),
                        'take_profit': self.data.close[0] * (1 + (stop_loss_pct * 2))  # 2:1 reward:risk
                    })
                    print(f"Buy order placed: {position_size:.2f} units at {self.data.close[0]:.5f}")

            # Evaluate sell entry rules (for short positions)
            sell_entry_rules = [rule for rule in self.p.entry_rules if rule.get("tradeType") == "sell"]
            if sell_entry_rules and not should_buy:  # Only check sell if buy wasn't triggered
                # Evaluate all sell rules
                sell_signals = [self.build_rule_condition(rule) for rule in sell_entry_rules]

                # Determine if we should sell based on logical operator
                should_sell = False
                if len(sell_signals) > 0:
                    if self.p.entrySellGroupOperator == "OR":
                        should_sell = any(sell_signals)
                    else:  # AND
                        should_sell = all(sell_signals)

                # Avoid logging when not needed to reduce output
                if should_sell:
                    print(f"✅ SELL SIGNAL TRIGGERED at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")

                    # Calculate position size using the same risk management as buy
                    stop_loss_pct = 0.02  # Default 2% stop loss
                    if self.p.stop_loss is not None:
                        if isinstance(self.p.stop_loss, str) and self.p.stop_loss.endswith('%'):
                            stop_loss_pct = float(self.p.stop_loss.rstrip('%')) / 100
                        else:
                            try:
                                stop_loss_pct = float(self.p.stop_loss)
                            except (ValueError, TypeError):
                                pass

                    # Risk 1% of account value
                    account_value = self.broker.getvalue()
                    risk_amount = account_value * 0.01  # 1% risk
                    price_risk = self.data.close[0] * stop_loss_pct  # Dollar amount of stop loss

                    # Calculate position size: risk amount divided by price risk
                    if price_risk > 0:
                        position_size = risk_amount / price_risk
                    else:
                        position_size = risk_amount / (self.data.close[0] * 0.02)  # Default 2% if issue

                    # Limit position size to maximum 10% of account
                    max_position_value = account_value * 0.10  # 10% of account value
                    max_position_size = max_position_value / self.data.close[0]
                    position_size = min(position_size, max_position_size)

                    print(f"Calculated position size: {position_size:.2f} units")
                    print(f"Account value: ${account_value:.2f}, Risk amount: ${risk_amount:.2f}")
                    print(f"Stop loss percent: {stop_loss_pct*100:.1f}%, Stop loss price: {self.data.close[0]*(1+stop_loss_pct):.5f}")

                    # Place sell order
                    self.sell(size=position_size)

                    # Record trade
                    self.trades.append({
                        'type': 'sell',
                        'entry_date': self.datetime.date(),
                        'entry_price': self.data.close[0],
                        'size': position_size,
                        'stop_loss': self.data.close[0] * (1 + stop_loss_pct),
                        'take_profit': self.data.close[0] * (1 - (stop_loss_pct * 2))  # 2:1 reward:risk
                    })
                    print(f"Sell order placed: {position_size:.2f} units at {self.data.close[0]:.5f}")

        # Check exit conditions or apply stop loss/take profit for existing positions
        elif position_size > 0:  # Long position
            # Check for stop loss or take profit first
            current_trade = next((t for t in self.trades if t.get('type') == 'buy' and 'exit_date' not in t), None)
            if current_trade:
                # Check if stop loss or take profit hit
                stop_loss_price = current_trade.get('stop_loss')
                take_profit_price = current_trade.get('take_profit')

                if stop_loss_price and self.data.close[0] <= stop_loss_price:
                    print(f"❌ STOP LOSS HIT at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()
                    current_trade.update({
                        'exit_date': self.datetime.date(),
                        'exit_price': self.data.close[0],
                        'exit_type': 'stop_loss',
                        'pnl': (self.data.close[0] - current_trade['entry_price']) * current_trade['size']
                    })
                    print(f"Position closed at {self.data.close[0]:.5f} (Stop Loss)")
                    return  # Skip further processing after closing position

                elif take_profit_price and self.data.close[0] >= take_profit_price:
                    print(f"💰 TAKE PROFIT HIT at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()
                    current_trade.update({
                        'exit_date': self.datetime.date(),
                        'exit_price': self.data.close[0],
                        'exit_type': 'take_profit',
                        'pnl': (self.data.close[0] - current_trade['entry_price']) * current_trade['size']
                    })
                    print(f"Position closed at {self.data.close[0]:.5f} (Take Profit)")
                    return  # Skip further processing after closing position

            # Evaluate exit rules if no stop loss/take profit hit
            buy_exit_rules = [rule for rule in self.p.exit_rules if rule.get("tradeType") == "buy"]
            if buy_exit_rules:
                # Evaluate all exit rules
                exit_signals = [self.build_rule_condition(rule) for rule in buy_exit_rules]

                # Determine if we should exit based on logical operator
                should_exit = False
                if len(exit_signals) > 0:
                    if self.p.exitBuyGroupOperator == "OR":
                        should_exit = any(exit_signals)
                    else:  # AND
                        should_exit = all(exit_signals)

                if should_exit:
                    print(f"✅ EXIT BUY SIGNAL at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()

                    # Update trade record
                    if current_trade:
                        current_trade.update({
                            'exit_date': self.datetime.date(),
                            'exit_price': self.data.close[0],
                            'exit_type': 'rule',
                            'pnl': (self.data.close[0] - current_trade['entry_price']) * current_trade['size']
                        })
                    print(f"Position closed at {self.data.close[0]:.5f} (Exit Rule)")

        elif position_size < 0:  # Short position
            # Check for stop loss or take profit first
            current_trade = next((t for t in self.trades if t.get('type') == 'sell' and 'exit_date' not in t), None)
            if current_trade:
                # Check if stop loss or take profit hit
                stop_loss_price = current_trade.get('stop_loss')
                take_profit_price = current_trade.get('take_profit')

                if stop_loss_price and self.data.close[0] >= stop_loss_price:
                    print(f"❌ STOP LOSS HIT at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()
                    current_trade.update({
                        'exit_date': self.datetime.date(),
                        'exit_price': self.data.close[0],
                        'exit_type': 'stop_loss',
                        'pnl': (current_trade['entry_price'] - self.data.close[0]) * current_trade['size']
                    })
                    print(f"Position closed at {self.data.close[0]:.5f} (Stop Loss)")
                    return  # Skip further processing after closing position

                elif take_profit_price and self.data.close[0] <= take_profit_price:
                    print(f"💰 TAKE PROFIT HIT at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()
                    current_trade.update({
                        'exit_date': self.datetime.date(),
                        'exit_price': self.data.close[0],
                        'exit_type': 'take_profit',
                        'pnl': (current_trade['entry_price'] - self.data.close[0]) * current_trade['size']
                    })
                    print(f"Position closed at {self.data.close[0]:.5f} (Take Profit)")
                    return  # Skip further processing after closing position

            # Evaluate exit rules if no stop loss/take profit hit
            sell_exit_rules = [rule for rule in self.p.exit_rules if rule.get("tradeType") == "sell"]
            if sell_exit_rules:
                # Evaluate all exit rules
                exit_signals = [self.build_rule_condition(rule) for rule in sell_exit_rules]

                # Determine if we should exit based on logical operator
                should_exit = False
                if len(exit_signals) > 0:
                    if self.p.exitSellGroupOperator == "OR":
                        should_exit = any(exit_signals)
                    else:  # AND
                        should_exit = all(exit_signals)

                if should_exit:
                    print(f"✅ EXIT SELL SIGNAL at {self.datetime.date()} - Price: {self.data.close[0]:.5f}")
                    self.close()

                    # Update trade record
                    if current_trade:
                        current_trade.update({
                            'exit_date': self.datetime.date(),
                            'exit_price': self.data.close[0],
                            'exit_type': 'rule',
                            'pnl': (current_trade['entry_price'] - self.data.close[0]) * current_trade['size']
                        })
                    print(f"Position closed at {self.data.close[0]:.5f} (Exit Rule)")

@https_fn.on_request()
def fetch_historical_data_from_polygon(req: https_fn.Request) -> https_fn.Response:
    """Fetch historical data from Polygon API with proper formatting."""
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            status=204,
            headers=cors_headers
        )

    try:
        # Get parameters from request
        symbol = req.args.get("symbol")
        timeframe = req.args.get("timeframe")
        start_date_str = req.args.get("start_date")
        end_date_str = req.args.get("end_date")

        if not all([symbol, timeframe]):
            return https_fn.Response(
                json.dumps({"error": "Missing required parameters"}),
                status=400,
                headers=cors_headers
            )

        # Format the forex pair correctly (add C: prefix for forex)
        if '/' in symbol:
            formatted_symbol = f"C:{symbol.replace('/', '')}"
        else:
            formatted_symbol = f"C:{symbol}"  # Add C: prefix if not present

        # Calculate date range if not provided (default to last 30 days)
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=30)

        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            except ValueError:
                start_date = end_date - timedelta(days=30)

        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                if end_date > datetime.now(timezone.utc):
                    end_date = datetime.now(timezone.utc)
            except ValueError:
                end_date = datetime.now(timezone.utc)

        # Ensure we're not requesting future data
        if end_date > datetime.now(timezone.utc):
            end_date = datetime.now(timezone.utc)
        if start_date > end_date:
            start_date = end_date - timedelta(days=30)

        # Format dates for API
        start_date_str = start_date.strftime("%Y-%m-%d")
        end_date_str = end_date.strftime("%Y-%m-%d")

        print(f"Fetching data for {formatted_symbol} from {start_date_str} to {end_date_str}")

        # Map timeframe to Polygon format
        timeframe_mapping = {
            "1m": ("minute", 1),
            "5m": ("minute", 5),
            "15m": ("minute", 15),
            "30m": ("minute", 30),
            "1h": ("hour", 1),
            "4h": ("hour", 4),
            "1d": ("day", 1)
        }

        if timeframe not in timeframe_mapping:
            return https_fn.Response(
                json.dumps({"error": f"Unsupported timeframe: {timeframe}"}),
                status=400,
                headers=cors_headers
            )

        base_timeframe, multiplier = timeframe_mapping[timeframe]

        # Get API key
        api_key = os.environ.get("POLYGON_API_KEY")
        if not api_key:
            return https_fn.Response(
                json.dumps({"error": "API key not configured"}),
                status=500,
                headers=cors_headers
            )

        # Construct API URL
        url = f"https://api.polygon.io/v2/aggs/ticker/{formatted_symbol}/range/{multiplier}/{base_timeframe}/{start_date_str}/{end_date_str}"

        # Make request with API key
        response = requests.get(url, params={
            "apiKey": api_key,
            "limit": 50000
        })

        # Handle rate limiting
        if response.status_code == 429:
            return https_fn.Response(
                json.dumps({"error": "Rate limit exceeded. Please try again later."}),
                status=429,
                headers=cors_headers
            )

        # Handle other errors
        if response.status_code != 200:
            return https_fn.Response(
                json.dumps({"error": f"API request failed with status {response.status_code}"}),
                status=response.status_code,
                headers=cors_headers
            )

        data = response.json()
        if data["resultsCount"] == 0:
            return https_fn.Response(
                json.dumps({"error": "No data available for the specified period"}),
                status=404,
                headers=cors_headers
            )

        # Process the results
        results = data["results"]
        df = pd.DataFrame(results)

        # Rename columns to match expected format
        df = df.rename(columns={
            "o": "open",
            "h": "high",
            "l": "low",
            "c": "close",
            "v": "volume",
            "t": "timestamp"
        })

        # Convert timestamp to datetime
        df["datetime"] = pd.to_datetime(df["timestamp"], unit="ms")
        df.set_index("datetime", inplace=True)
        df = df.sort_index()

        # Convert to records format
        records = df.reset_index().to_dict("records")

        # Format datetime objects to ISO strings with explicit UTC timezone
        for record in records:
            # Ensure the datetime is in UTC and includes the 'Z' suffix to indicate UTC
            utc_dt = record["datetime"].tz_localize('UTC') if record["datetime"].tz is None else record["datetime"].astimezone(timezone.utc)
            record["datetime"] = utc_dt.isoformat().replace('+00:00', 'Z')

        # Add logging after fetching data
        if isinstance(data, list) and len(data) > 0:
            print("[Polygon] First candle datetime:", data[0].get('datetime'))
            print("[Polygon] Last candle datetime:", data[-1].get('datetime'))
        else:
            print("[Polygon] Data is not a non-empty list. Type:", type(data), "Value sample:", str(data)[:500])

        # Extract candles from Polygon response
        candles = data.get('results', []) if isinstance(data, dict) else []
        if isinstance(candles, list) and len(candles) > 0:
            first = candles[0]
            last = candles[-1]
            print("[Polygon] First candle raw:", first)
            print("[Polygon] Last candle raw:", last)
            print("[Polygon] First candle UTC:", datetime.fromtimestamp(first['t']/1000, tz=timezone.utc).isoformat())
            print("[Polygon] Last candle UTC:", datetime.fromtimestamp(last['t']/1000, tz=timezone.utc).isoformat())
        else:
            print("[Polygon] No candles found in 'results'. Type:", type(candles), "Value sample:", str(candles)[:500])

        return https_fn.Response(
            json.dumps({
                "data": records,
                "success": True,
                "info": {
                    "symbol": formatted_symbol,
                    "timeframe": timeframe,
                    "start_date": start_date_str,
                    "end_date": end_date_str,
                    "count": len(records)
                }
            }),
            status=200,
            headers=cors_headers
        )

    except Exception as e:
        print(f"Error in fetch_historical_data_from_polygon: {str(e)}")
        traceback.print_exc()
        return https_fn.Response(
            json.dumps({"error": str(e)}),
            status=500,
            headers=cors_headers
        )

def fetch_historical_data_from_polygon_impl(symbol, timeframe, start_date=None, end_date=None):
    """Fetch historical data from the Polygon.io API with retry logic and rate limiting."""
    try:
        print(f"🔄 Fetching historical data for {symbol} from Polygon...")
        print(f"📅 Date range: {start_date} to {end_date}")

        # If no dates provided, default to the last 30 days instead of 365
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")

        # Get API key from stored value
        api_key = os.environ.get("POLYGON_API_KEY")
        if not api_key:
            raise ValueError("No API key found. Please add your Polygon API key to the environment variables.")

        # Map timeframe to the format expected by Polygon API
        timeframe_mapping = {
            "1m": ("minute", 1),
            "5m": ("minute", 5),
            "15m": ("minute", 15),
            "30m": ("minute", 30),
            "1h": ("hour", 1),
            "4h": ("hour", 4),
            "1d": ("day", 1)
        }

        if timeframe not in timeframe_mapping:
            raise ValueError(f"Unsupported timeframe: {timeframe}")

        base_timeframe, multiplier = timeframe_mapping[timeframe]

        # Format the symbol for Forex (add C: prefix for forex)
        if '/' in symbol:
            formatted_symbol = f"C:{symbol.replace('/', '')}"
        else:
            formatted_symbol = symbol

        # Convert dates to datetime objects for chunking
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # Initialize empty DataFrame to store all results
        all_data = pd.DataFrame()

        # Use smaller chunk size (2 days) and implement retry logic
        chunk_size = timedelta(days=2)
        current_start = start_dt
        max_retries = 3
        base_delay = 2  # Base delay in seconds

        while current_start < end_dt:
            # Calculate the end of this chunk
            current_end = min(current_start + chunk_size, end_dt)

            # Format dates for API
            current_start_str = current_start.strftime("%Y-%m-%d")
            current_end_str = current_end.strftime("%Y-%m-%d")

            print(f"📊 Fetching chunk: {current_start_str} to {current_end_str}")

            # Construct API URL
            url = f"https://api.polygon.io/v2/aggs/ticker/{formatted_symbol}/range/{multiplier}/{base_timeframe}/{current_start_str}/{current_end_str}"
            print(f"🔗 API URL: {url}")

            # Add API key to query parameters
            params = {
                "apiKey": api_key,
                "limit": 50000  # Request maximum number of results
            }

            # Implement retry logic with exponential backoff
            for retry in range(max_retries):
                try:
                    # Add delay based on retry attempt (exponential backoff)
                    if retry > 0:
                        delay = base_delay * (2 ** (retry - 1))
                        print(f"⏳ Retrying in {delay} seconds (attempt {retry + 1}/{max_retries})")
                        time.sleep(delay)

                    response = requests.get(url, params=params)

                    # Handle rate limit specifically
                    if response.status_code == 429:
                        if retry < max_retries - 1:
                            # Get retry-after header or use default
                            retry_after = int(response.headers.get('Retry-After', 60))
                            print(f"⏳ Rate limited. Waiting {retry_after} seconds...")
                            time.sleep(retry_after)
                            continue
                        else:
                            raise Exception("Rate limit exceeded. Please try again later.")

                    response.raise_for_status()
                    data = response.json()

                    if data["status"] == "OK" and data["resultsCount"] > 0:
                        # Extract the results
                        results = data["results"]

                        # Create DataFrame
                        chunk_df = pd.DataFrame(results)

                        # Rename columns to match expected format
                        chunk_df = chunk_df.rename(columns={
                            "o": "open",
                            "h": "high",
                            "l": "low",
                            "c": "close",
                            "v": "volume",
                            "vw": "vw",
                            "n": "n"
                        })

                        # Convert timestamp to datetime
                        chunk_df["datetime"] = pd.to_datetime(chunk_df["t"], unit="ms")

                        # Set datetime as index
                        chunk_df.set_index("datetime", inplace=True)

                        # Sort by datetime
                        chunk_df.sort_index(inplace=True)

                        # Append to the main DataFrame
                        all_data = pd.concat([all_data, chunk_df])

                        print(f"✅ Fetched {len(chunk_df)} rows for this chunk")

                        # Success, break retry loop
                        break
                    else:
                        print(f"⚠️ No data found for period {current_start_str} to {current_end_str}")
                        break

                except requests.exceptions.RequestException as e:
                    if retry == max_retries - 1:
                        print(f"❌ Failed to fetch data after {max_retries} retries: {str(e)}")
                        raise
                    print(f"⚠️ Request failed (attempt {retry + 1}/{max_retries}): {str(e)}")

            # Move to next chunk
            current_start = current_end + timedelta(days=1)

            # Add a small delay between chunks to avoid rate limits
            time.sleep(0.5)

        # If we got data, process it
        if not all_data.empty:
            # Remove duplicates
            all_data = all_data[~all_data.index.duplicated(keep='first')]

            # Sort by datetime
            all_data.sort_index(inplace=True)

            # Ensure all required columns are numeric
            for col in ['open', 'high', 'low', 'close', 'volume']:
                all_data[col] = pd.to_numeric(all_data[col], errors='coerce')

            # Drop any rows with missing values
            all_data = all_data.dropna(subset=['open', 'high', 'low', 'close'])

            print(f"✅ Successfully fetched a total of {len(all_data)} rows of data from Polygon")
            print(f"📅 Data range: {all_data.index.min()} to {all_data.index.max()}")

            return all_data
        else:
            print(f"❌ No data found for the specified date range")
            return None

    except Exception as e:
        print(f"❌ Error fetching data from Polygon: {str(e)}")
        traceback.print_exc()
        return None

@https_fn.on_request()
def signup(req: https_fn.Request) -> https_fn.Response:
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight OPTIONS request
    if req.method == "OPTIONS":
        return https_fn.Response(
            json.dumps({"message": "Preflight CORS OK"}),
            status=204,
            mimetype="application/json",
            headers=cors_headers,
        )

    if req.method != "POST":
        return https_fn.Response(
            json.dumps({"message": "Method Not Allowed"}),
            status=405,
            mimetype="application/json",
            headers=cors_headers,
        )

    try:
        # Force JSON parsing even if headers are off
        data = req.get_json(force=True)
    except Exception as e:
        print("Error parsing JSON:", e)
        return https_fn.Response(
            json.dumps({"message": "Invalid JSON payload"}),
            status=400,
            mimetype="application/json",
            headers=cors_headers,
        )

    try:
        signup_data = SignupRequest(**data)
        user_ref = db.collection("users").document(signup_data.firebase_uid)
        if user_ref.get().exists:
            return https_fn.Response(
                json.dumps({"message": "User already registered."}),
                status=400,
                mimetype="application/json",
                headers=cors_headers,
            )
        user_ref.set({
            "email": signup_data.email,
            "has_api_key": False,
            "api_key": None,
            "account_id": None,
        })
        return https_fn.Response(
            json.dumps({"message": "Signup successful!", "user_id": signup_data.firebase_uid}),
            status=201,
            mimetype="application/json",
            headers=cors_headers,
        )
    except Exception as e:
        print("Error in signup process:", e)
        return https_fn.Response(
            json.dumps({"message": str(e)}),
            status=500,
            mimetype="application/json",
            headers=cors_headers,
        )

@https_fn.on_request()
def get_user(req: https_fn.Request) -> https_fn.Response:
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle OPTIONS preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            json.dumps({"message": "Preflight CORS ok"}),
            status=204,
            mimetype="application/json",
            headers=cors_headers
        )

    if req.method != 'GET':
        return https_fn.Response(
            json.dumps({"message": "Method Not Allowed"}),
            status=405,
            mimetype='application/json',
            headers=cors_headers
        )
    try:
        firebase_uid = req.args.get("firebase_uid")
        if not firebase_uid:
            return https_fn.Response(
                json.dumps({"message": "firebase_uid is required"}),
                status=400,
                mimetype='application/json',
                headers=cors_headers
            )
        print("Received firebase_uid:", firebase_uid)
        user_ref = db.collection("users").document(firebase_uid)
        user_doc = user_ref.get()
        if not user_doc.exists:
            return https_fn.Response(
                json.dumps({"message": "User not found"}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )
        user_data = user_doc.to_dict()
        return https_fn.Response(
            json.dumps({
                "user_id": firebase_uid,
                "email": user_data.get("email"),
                "has_api_key": user_data.get("has_api_key", False)
            }),
            status=200,
            mimetype='application/json',
            headers=cors_headers
        )
    except Exception as e:
        return https_fn.Response(
            json.dumps({"message": str(e)}),
            status=500,
            mimetype='application/json',
            headers=cors_headers
        )

@https_fn.on_request()
def register_api_key(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight requests
    if req.method == "OPTIONS":
        return https_fn.Response(
            json.dumps({"message": "Preflight CORS OK"}),
            status=204,
            mimetype="application/json",
            headers=cors_headers
        )

    if req.method != "POST":
        return https_fn.Response(
            json.dumps({"message": "Method Not Allowed"}),
            status=405,
            mimetype="application/json",
            headers=cors_headers
        )

    try:
        data = req.get_json()
        firebase_uid = data.get('firebase_uid')
        api_key = data.get('api_key')

        if not firebase_uid or not api_key:
            return https_fn.Response(json.dumps({'error': 'Missing required fields'}),
                                   status=400,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Verify the user exists
        user = auth.get_user(firebase_uid)
        if not user:
            return https_fn.Response(json.dumps({'error': 'User not found'}),
                status=404,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Validate the API key by making a request to OANDA
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # First check if the API key is valid
        account_response = requests.get(f"{OANDA_API_URL}/accounts", headers=headers)
        if account_response.status_code != 200:
            return https_fn.Response(
                json.dumps({"message": "Invalid API key or unauthorized request."}),
                status=401,
                mimetype='application/json',
                headers=cors_headers
            )

        account_data = account_response.json()
        if not account_data.get("accounts"):
            return https_fn.Response(
                json.dumps({"message": "No OANDA accounts found for this API key."}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )

        account_id = account_data["accounts"][0]["id"]

        # If we get here, the API key is valid. Now update the user's API key in Firestore
        user_ref = db.collection('users').document(firebase_uid)
        user_ref.update({
            'api_key': api_key,
            'account_id': account_id,
            'has_api_key': True,
            'api_key_updated_at': firestore.SERVER_TIMESTAMP
        })

        return https_fn.Response(json.dumps({
            'message': 'API key updated successfully',
            'account_id': account_id,
            'timestamp': datetime.utcnow().isoformat()
        }), status=200, mimetype="application/json", headers=cors_headers)

    except Exception as e:
        print(f"Error updating API key: {str(e)}")
        return https_fn.Response(json.dumps({
            'error': 'Failed to update API key',
            'details': str(e)
        }), status=500, mimetype="application/json", headers=cors_headers)

@https_fn.on_request()
def change_password(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }
    if req.method == "OPTIONS":
        return https_fn.Response(json.dumps({"message": "Preflight CORS OK"}),
                                   status=204,
                                   mimetype="application/json",
                                   headers=cors_headers)
    if req.method != "POST":
        return https_fn.Response(json.dumps({"message": "Method Not Allowed"}),
                                   status=405,
                                   mimetype="application/json",
                                   headers=cors_headers)
    try:
        data = req.get_json()
        firebase_uid = data.get('firebase_uid')
        new_password = data.get('new_password')

        if not firebase_uid or not new_password:
            return https_fn.Response(json.dumps({'error': 'Missing required fields'}),
                                   status=400,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Verify the user exists
        user = auth.get_user(firebase_uid)
        if not user:
            return https_fn.Response(json.dumps({'error': 'User not found'}),
                                   status=404,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Update the user's password
        auth.update_user(
            firebase_uid,
            password=new_password
        )

        return https_fn.Response(json.dumps({
            'message': 'Password updated successfully',
            'timestamp': datetime.utcnow().isoformat()
        }), status=200, mimetype="application/json", headers=cors_headers)

    except Exception as e:
        print(f"Error changing password: {str(e)}")
        return https_fn.Response(json.dumps({
            'error': 'Failed to change password',
            'details': str(e)
        }), status=500, mimetype="application/json", headers=cors_headers)

@https_fn.on_request()
def delete_account(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }
    if req.method == "OPTIONS":
        return https_fn.Response(json.dumps({"message": "Preflight CORS OK"}),
                                   status=204,
                                   mimetype="application/json",
                                   headers=cors_headers)
    if req.method != "POST":
        return https_fn.Response(json.dumps({"message": "Method Not Allowed"}),
                                   status=405,
                                   mimetype="application/json",
                                   headers=cors_headers)
    try:
        data = req.get_json()
        firebase_uid = data.get('firebase_uid')

        if not firebase_uid:
            return https_fn.Response(json.dumps({'error': 'Missing user ID'}),
                                   status=400,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Verify the user exists
        user = auth.get_user(firebase_uid)
        if not user:
            return https_fn.Response(json.dumps({'error': 'User not found'}),
                                   status=404,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Delete user data from Firestore
        user_ref = db.collection('users').document(firebase_uid)
        user_ref.delete()

        # Delete the user from Firebase Auth
        auth.delete_user(firebase_uid)

        return https_fn.Response(json.dumps({
            'message': 'Account deleted successfully',
            'timestamp': datetime.utcnow().isoformat()
        }), status=200, mimetype="application/json", headers=cors_headers)

    except Exception as e:
        print(f"Error deleting account: {str(e)}")
        return https_fn.Response(json.dumps({
            'error': 'Failed to delete account',
            'details': str(e)
        }), status=500, mimetype="application/json", headers=cors_headers)

@https_fn.on_request()
def get_oanda_account(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight OPTIONS request
    if req.method == "OPTIONS":
        return https_fn.Response(
            json.dumps({"message": "Preflight CORS ok"}),
            status=204,
            mimetype="application/json",
            headers=cors_headers
        )

    if req.method != 'GET':
        return https_fn.Response(
            json.dumps({"message": "Method Not Allowed"}),
            status=405,
            mimetype='application/json',
            headers=cors_headers
        )
    try:
        firebase_uid = req.args.get("firebase_uid")
        if not firebase_uid:
            return https_fn.Response(
                json.dumps({"message": "firebase_uid is required"}),
                status=400,
                mimetype='application/json',
                headers=cors_headers
            )

        # Retrieve the user's document from Firestore.
        user_doc = db.collection("users").document(firebase_uid).get()
        if not user_doc.exists or not user_doc.to_dict().get("api_key"):
            return https_fn.Response(
                json.dumps({"message": "API key not found"}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )

        api_key = user_doc.to_dict().get("api_key")
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        print(f"Fetching OANDA data for user {firebase_uid}...")
        account_response = requests.get(f"{OANDA_API_URL}/accounts", headers=headers)
        print("Account response:", account_response.status_code, account_response.text)

        if account_response.status_code == 401:
            return https_fn.Response(
                json.dumps({"message": "Invalid API key. Please enter a valid key."}),
                status=401,
                mimetype='application/json',
                headers=cors_headers
            )
        if account_response.status_code != 200:
            return https_fn.Response(
                json.dumps({"message": "Failed to fetch account data", "status": account_response.status_code}),
                status=account_response.status_code,
                mimetype='application/json',
                headers=cors_headers
            )

        accounts = account_response.json().get("accounts", [])
        if not accounts:
            return https_fn.Response(
                json.dumps({"message": "No OANDA accounts found for this API Key. Please check your credentials."}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )

        account_id = accounts[0]["id"]
        print(f"Using account ID: {account_id}")

        # Fetch account balance and open trades
        balance_response = requests.get(f"{OANDA_API_URL}/accounts/{account_id}/summary", headers=headers)
        trades_response = requests.get(f"{OANDA_API_URL}/accounts/{account_id}/openTrades", headers=headers)

        print("Balance response:", balance_response.status_code, balance_response.text)
        print("Trades response:", trades_response.status_code, trades_response.text)

        if balance_response.status_code == 401 or trades_response.status_code == 401:
            return https_fn.Response(
                json.dumps({"message": "Unauthorized request. Please check your API Key."}),
                status=401,
                mimetype='application/json',
                headers=cors_headers
            )
        if balance_response.status_code != 200 or trades_response.status_code != 200:
            return https_fn.Response(
                json.dumps({"message": "Failed to fetch account details"}),
                status=500,
                mimetype='application/json',
                headers=cors_headers
            )

        balance_data = balance_response.json()
        trades_data = trades_response.json()

        result = {
            "account_id": account_id,
            "balance": balance_data.get("account", {}).get("balance"),
            "open_trades": trades_data.get("trades", [])
        }
        return https_fn.Response(
            json.dumps(result),
            status=200,
            mimetype='application/json',
            headers=cors_headers
        )
    except Exception as e:
        print("Error:", e)
        return https_fn.Response(
            json.dumps({"message": str(e)}),
            status=500,
            mimetype='application/json',
            headers=cors_headers
        )

@https_fn.on_request()
def publish_strategy(req: https_fn.Request) -> https_fn.Response:
    """HTTPS callable function to publish a new strategy for execution."""
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
    }

    # Handle preflight requests
    if req.method == "OPTIONS":
        return https_fn.Response("", status=204, headers=cors_headers)

    if req.method != "POST":
        return https_fn.Response(
            json.dumps({"status": "error", "message": "Method Not Allowed"}),
            status=405,
            mimetype="application/json",
            headers=cors_headers
        )

    logging.info("Publishing new strategy")

    try:
        # Parse request body
        request_json = req.get_json(silent=True)
        if not request_json:
            logging.error("Invalid request body - no JSON found")
            return https_fn.Response(
                json.dumps({"status": "error", "message": "Invalid request body"}),
                status=400,
                mimetype="application/json",
                headers=cors_headers
            )

        # Extract required fields
        user_id = request_json.get('user_id')
        strategy_id = request_json.get('strategy_id')
        strategy_json = request_json.get('strategy_json')

        if not all([user_id, strategy_id, strategy_json]):
            missing = []
            if not user_id: missing.append("user_id")
            if not strategy_id: missing.append("strategy_id")
            if not strategy_json: missing.append("strategy_json")
            logging.error(f"Missing required fields: {', '.join(missing)}")
            return https_fn.Response(
                json.dumps({
                    "status": "error",
                    "message": f"Missing required fields: {', '.join(missing)}"
                }),
                status=400,
                mimetype="application/json",
                headers=cors_headers
            )

        logging.info(f"Processing strategy for user {user_id}, strategy {strategy_id}")

        try:
            # Parse the strategy JSON
            strategy_data = json.loads(strategy_json)

            # Initialize Pub/Sub client
            if os.getenv('FUNCTIONS_EMULATOR'):
                os.environ["PUBSUB_EMULATOR_HOST"] = "localhost:8085"
                logging.info("Running in emulator mode - using PubSub emulator")

            publisher = pubsub_v1.PublisherClient()
            topic_path = publisher.topic_path("oryntrade", "strategy-execution")

            # Try to create the topic if it doesn't exist
            try:
                publisher.create_topic(request={"name": topic_path})
                logging.info(f"Created topic: {topic_path}")
            except exceptions.AlreadyExists:
                logging.info("Topic already exists")
            except Exception as topic_error:
                logging.warning(f"Error checking/creating topic: {str(topic_error)}")

            # Create message data with strategy_json
            message_data = {
                "user_id": user_id,
                "strategy_id": strategy_id
            }

            # Convert to JSON
            message_json = json.dumps(message_data)

            # Publish the message with attributes
            strategy_name = strategy_data.get("name", "Unnamed Strategy")
            attributes = {
                "user_id": user_id,
                "strategy_id": strategy_id,
                "strategy_name": strategy_name,
                "is_test": "false"
            }

            try:
                future = publisher.publish(
                    topic_path,
                    data=message_json.encode("utf-8"),
                    **attributes,
                    timeout=30.0
                )

                message_id = future.result()
                logging.info(f"Published message with ID: {message_id}")
            except Exception as publish_error:
                logging.error(f"Error publishing message: {str(publish_error)}")
                raise publish_error

            # Store strategy in Firestore
            strategy_ref = db.collection("users").document(user_id).collection("submittedStrategies").document(strategy_id)

            # Format minimal human readable info
            human_readable_rules = {
                "strategy_info": {
                    "name": strategy_data.get("name", "Unnamed Strategy"),
                    "instrument": strategy_data.get("instruments", "Unknown"),
                    "timeframe": strategy_data.get("timeframe", "Unknown")
                }
            }

            # Store in Firestore with original strategy_json
            store_data = {
                "strategy_id": strategy_id,
                "status": "pending",
                "strategy_json": strategy_json,
                "human_readable_rules": human_readable_rules,
                "lastHeartbeat": firestore.SERVER_TIMESTAMP
            }

            # Store with merge to avoid overwriting any existing fields
            strategy_ref.set(store_data, merge=True)
            logging.info(f"Strategy stored in Firestore for user {user_id}")

            return https_fn.Response(
                json.dumps({"status": "success", "message": "Strategy published successfully"}),
                status=200,
                mimetype="application/json",
                headers=cors_headers
            )

        except json.JSONDecodeError as jde:
            logging.error(f"Invalid strategy JSON format: {str(jde)}")
            return https_fn.Response(
                json.dumps({"status": "error", "message": "Invalid strategy JSON format"}),
                status=400,
                mimetype="application/json",
                headers=cors_headers
            )
        except Exception as e:
            logging.error(f"Error publishing strategy: {str(e)}", exc_info=True)
            return https_fn.Response(
                json.dumps({"status": "error", "message": f"Failed to process strategy: {str(e)}"}),
                status=500,
                mimetype="application/json",
                headers=cors_headers
            )

    except Exception as e:
        logging.error(f"Error in publish_strategy: {str(e)}", exc_info=True)
        return https_fn.Response(
            json.dumps({"status": "error", "message": f"Failed to process strategy: {str(e)}"}),
            status=500,
            mimetype="application/json",
            headers=cors_headers
        )

@https_fn.on_request()
def handle_email_signup(req: https_fn.Request) -> https_fn.Response:
    print("=== STARTING handle_email_signup FUNCTION ===")

    # Handle CORS preflight requests
    if req.method == 'OPTIONS':
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': 'Content-Type',
        }
        return https_fn.Response('', status=204, headers=headers)

    if req.method != 'POST':
        print("Error: Method not allowed")
        return https_fn.Response('Method not allowed', status=405)

    try:
        # Get request data
        data = req.get_json()
        print(f"Received data: {json.dumps(data, indent=2)}")

        if not data:
            print("Error: No data provided")
            return https_fn.Response('No data provided', status=400)

        email = data.get('email')
        if not email:
            print("Error: No email provided")
            return https_fn.Response('Email is required', status=400)

        print(f"Processing email signup for: {email}")

        # Check if email already exists in Firestore
        db = firestore.client()
        existing_signup = db.collection('email_signups').where('email', '==', email).limit(1).get()

        if existing_signup:
            print(f"Email {email} already exists in signups")
            return https_fn.Response(
                json.dumps({
                    "message": "You're already subscribed to our early access program!",
                    "status": "already_subscribed"
                }),
                status=200,
                mimetype="application/json",
                headers={
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            )

        # Get SendGrid API key
        sendgrid_api_key = os.environ.get('SENDGRID_API_KEY')
        if not sendgrid_api_key:
            print("Error: SendGrid API key not found in environment variables")
            return https_fn.Response('SendGrid API key not configured', status=500)

        print(f"Using SendGrid API key: {sendgrid_api_key[:5]}...{sendgrid_api_key[-4:]}")

        # Generate a unique discount code
        discount_code = f"ORYN50_{generateId()[:8].upper()}"

        # Store discount code in Firestore
        try:
            print("Storing discount code in Firestore...")
            db.collection('discount_codes').add({
                'email': email,
                'code': discount_code,
                'discount_percentage': 50,
                'valid_for': '1 year',
                'created_at': firestore.SERVER_TIMESTAMP,
                'status': 'pending'
            })
        except Exception as e:
            print(f"Error storing discount code in Firestore: {str(e)}")

        # Generate unsubscribe token
        unsubscribe_token = generateId()

        # Store unsubscribe token in Firestore
        try:
            print("Storing unsubscribe token in Firestore...")
            db.collection('unsubscribe_tokens').add({
                'email': email,
                'token': unsubscribe_token,
                'created_at': firestore.SERVER_TIMESTAMP,
                'used': False
            })
        except Exception as e:
            print(f"Error storing unsubscribe token in Firestore: {str(e)}")

        # HTML Email Template
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Oryn - Early Access</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f4f4f4;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #ffffff;
                }}
                .header {{
                    background: linear-gradient(135deg, #FFB800 0%, #FFA000 100%);
                    color: white;
                    padding: 40px 20px;
                    text-align: center;
                    border-radius: 10px 10px 0 0;
                }}
                .content {{
                    padding: 30px 20px;
                }}
                .offer-box {{
                    background-color: #f8f9fa;
                    border: 2px solid #FFB800;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: center;
                }}
                .button {{
                    display: inline-block;
                    padding: 12px 24px;
                    background: linear-gradient(135deg, #FFB800 0%, #FFA000 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 6px;
                    font-weight: bold;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    padding: 20px;
                    color: #666;
                    font-size: 14px;
                    border-top: 1px solid #eee;
                }}
                .unsubscribe {{
                    color: #666;
                    font-size: 12px;
                    text-align: center;
                    margin-top: 20px;
                }}
                .unsubscribe a {{
                    color: #FFB800;
                    text-decoration: none;
                }}
                .unsubscribe a:hover {{
                    text-decoration: underline;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to Oryn!</h1>
                    <p>Thank you for joining our early access program</p>
                </div>

                <div class="content">
                    <p>We're excited to have you on board! As an early access member, you'll be among the first to experience the future of AI-powered trading.</p>

                    <p>What's next?</p>
                    <ul>
                        <li>We'll keep you updated on our launch progress</li>
                        <li>You'll receive exclusive insights about our features</li>
                        <li>You'll get early access to our trading platform</li>
                    </ul>

                    <div class="offer-box">
                        <h2>🎉 Special Early Access Offer</h2>
                        <p>As a thank you for joining early, we're giving you:</p>
                        <p><strong>50% off for the entire first year</strong> when we launch!</p>
                        <p>Simply use this email address when you sign up at launch, and your discount will be automatically applied.</p>
                    </div>

                    <p>Stay tuned for updates about our launch date and how to redeem your special offer.</p>

                    <p>Best regards,<br>The Oryn Team</p>
                </div>

                <div class="footer">
                    <p>© 2024 A&Y Ventures. All rights reserved.</p>
                    <div class="unsubscribe">
                        <p>Don't want to receive these emails? <a href="https://oryntrade.com/unsubscribe?token={unsubscribe_token}">Unsubscribe here</a></p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        # Send welcome email
        try:
            print("Sending welcome email...")
            email_payload = {
                "personalizations": [{
                    "to": [{"email": email}],
                    "subject": "Welcome to Oryn - Early Access & Special Offer Inside!"
                }],
                "from": {"email": "<EMAIL>", "name": "Oryn Team"},
                "content": [{
                    "type": "text/html",
                    "value": html_content
                }]
            }

            email_response = requests.post(
                "https://api.sendgrid.com/v3/mail/send",
                headers={
                    "Authorization": f"Bearer {sendgrid_api_key}",
                    "Content-Type": "application/json"
                },
                json=email_payload
            )

            print(f"SendGrid Email Response Status Code: {email_response.status_code}")
            print(f"SendGrid Email Response Body: {email_response.text}")

            if email_response.status_code not in [200, 201, 202]:
                print(f"Warning: Failed to send welcome email. Status: {email_response.status_code}")
                print(f"Error details: {email_response.text}")
            else:
                print(f"Successfully sent welcome email. Status: {email_response.status_code}")

        except Exception as e:
            print(f"Warning: Error sending welcome email: {str(e)}")

        # Add contact to SendGrid
        try:
            print("Adding contact to SendGrid...")
            contact_payload = {
                "contacts": [{
                    "email": email,
                    "first_name": "Early Access User"
                }]
            }
            print(f"Contact payload: {json.dumps(contact_payload, indent=2)}")

            contact_response = requests.put(
                "https://api.sendgrid.com/v3/marketing/contacts",
                headers={
                    "Authorization": f"Bearer {sendgrid_api_key}",
                    "Content-Type": "application/json"
                },
                json=contact_payload
            )

            print(f"SendGrid Response Status Code: {contact_response.status_code}")
            print(f"SendGrid Response Headers: {dict(contact_response.headers)}")
            print(f"SendGrid Response Body: {contact_response.text}")

            if contact_response.status_code not in [200, 201, 202]:
                print(f"Warning: Failed to add contact to SendGrid. Status: {contact_response.status_code}")
                print(f"Error details: {contact_response.text}")
            else:
                print(f"Successfully added contact to SendGrid. Status: {contact_response.status_code}")
                if contact_response.status_code == 202:
                    print(f"Contact will be processed asynchronously. Job ID: {contact_response.json().get('job_id')}")

        except Exception as e:
            print(f"Warning: Error adding contact to SendGrid: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Error details: {e.__dict__}")

        # Store email in Firestore
        try:
            print("Storing email in Firestore...")
            db = firestore.client()
            doc_ref = db.collection('email_signups').add({
                'email': email,
                'timestamp': firestore.SERVER_TIMESTAMP,
                'status': 'pending',
                'discount_code': discount_code,
                'unsubscribe_token': unsubscribe_token
            })
            print(f"Successfully stored email in Firestore with ID: {doc_ref[1].id}")
        except Exception as e:
            print(f"Error storing email in Firestore: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Error details: {e.__dict__}")

        print("Email signup process completed successfully")
        return https_fn.Response(
            json.dumps({"message": "Email signup successful"}),
            status=200,
            mimetype="application/json",
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )
    except Exception as e:
        print(f"Error in handle_email_signup: {str(e)}")
        return https_fn.Response(
            json.dumps({"error": str(e)}),
            status=500,
            mimetype="application/json",
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )

@https_fn.on_request()
def handle_contact_form(req: https_fn.Request) -> https_fn.Response:
    print("Starting handle_contact_form function")
    print("Environment variables:")
    print("SENDGRID_API_KEY:", os.environ.get('SENDGRID_API_KEY'))

    # Handle CORS preflight requests
    if req.method == 'OPTIONS':
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': 'Content-Type',
        }
        return https_fn.Response('', status=204, headers=headers)

    # Only allow POST requests
    if req.method != 'POST':
        return https_fn.Response('Method not allowed', status=405)

    try:
        # Get request data
        data = req.get_json()
        if not data:
            return https_fn.Response('No data provided', status=400)

        # Validate required fields
        required_fields = ['name', 'email', 'subject', 'message']
        for field in required_fields:
            if field not in data:
                return https_fn.Response(f'Missing required field: {field}', status=400)

        # Get data
        name = data['name']
        email = data['email']
        subject = data['subject']
        message_text = data['message']

        # Get SendGrid API key
        sendgrid_api_key = os.environ.get('SENDGRID_API_KEY')
        if not sendgrid_api_key:
            print("Error: SendGrid API key not found in environment variables")
            return https_fn.Response('SendGrid API key not configured', status=500)

        print(f"Using SendGrid API key: {sendgrid_api_key[:5]}...{sendgrid_api_key[-4:]}")

        # First, add the contact to SendGrid
        try:
            print("Adding contact to SendGrid...")
            contact_payload = {
                "contacts": [{
                    "email": email,
                    "first_name": name
                }]
            }

            contact_response = requests.put(
                "https://api.sendgrid.com/v3/marketing/contacts",
                headers={
                    "Authorization": f"Bearer {sendgrid_api_key}",
                    "Content-Type": "application/json"
                },
                json=contact_payload
            )

            if contact_response.status_code not in [200, 201, 202]:
                print(f"Warning: Failed to add contact to SendGrid. Status: {contact_response.status_code}")
                print(f"Error details: {contact_response.text}")
            else:
                print(f"Successfully added contact to SendGrid. Status: {contact_response.status_code}")
                if contact_response.status_code == 202:
                    print(f"Contact will be processed asynchronously. Job ID: {contact_response.json().get('job_id')}")
        except Exception as e:
            print(f"Warning: Error adding contact to SendGrid: {str(e)}")

        # Prepare the email payload
        email_payload = {
            "personalizations": [{
                "to": [{"email": "<EMAIL>"}]
            }],
            "from": {"email": "<EMAIL>"},
            "subject": f"[Contact Form] From {name} ({email}): {subject}",
            "content": [{
                "type": "text/html",
                "value": f"""
                    <h2>New Contact Form Submission</h2>
                    <p><strong>From:</strong> {name} ({email})</p>
                    <p><strong>Subject:</strong> {subject}</p>
                    <p><strong>Message:</strong></p>
                    <p>{message_text.replace('\n', '<br>')}</p>
                """
            }]
        }

        print("Email payload prepared:", json.dumps(email_payload, indent=2))

        # Send email using SendGrid API
        try:
            print("Sending email via SendGrid API...")
            headers = {
                "Authorization": f"Bearer {sendgrid_api_key}",
                "Content-Type": "application/json"
            }
            print("Request headers:", json.dumps(headers, indent=2))

            response = requests.post(
                "https://api.sendgrid.com/v3/mail/send",
                headers=headers,
                json=email_payload
            )

            print(f"SendGrid Response Status Code: {response.status_code}")
            print(f"SendGrid Response Headers: {dict(response.headers)}")
            print(f"SendGrid Response Body: {response.text}")

            if response.status_code in [200, 201, 202]:
                print("Email sent successfully!")
                return https_fn.Response(
                    json.dumps({"message": "Message sent successfully"}),
                    status=200,
                    mimetype="application/json",
                    headers={
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'POST',
                        'Access-Control-Allow-Headers': 'Content-Type',
                    }
                )
            else:
                print(f"Error sending email. Status code: {response.status_code}")
                print(f"Error details: {response.text}")
                return https_fn.Response(
                    json.dumps({"message": "Failed to send message", "error": response.text}),
                    status=500,
                    mimetype="application/json",
                    headers={
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'POST',
                        'Access-Control-Allow-Headers': 'Content-Type',
                    }
                )

        except Exception as e:
            print(f"Error sending email: {str(e)}")
            print(f"Error type: {type(e)}")
            print(f"Error details: {e.__dict__}")
            return https_fn.Response('Failed to send message', status=500)

    except Exception as e:
        print(f"Error processing request: {str(e)}")
        print(f"Error type: {type(e)}")
        print(f"Error details: {e.__dict__}")
        return https_fn.Response('Internal server error', status=500)

@https_fn.on_request()
def handle_feature_vote(req: https_fn.Request) -> https_fn.Response:
    """Handle feature poll votes."""
    # Define CORS headers
    cors_headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }

    # Handle preflight requests
    if req.method == 'OPTIONS':
        return https_fn.Response('', status=204, headers=cors_headers)

    if req.method != 'POST':
        return https_fn.Response('Method not allowed', status=405, headers=cors_headers)

    try:
        data = req.get_json()
        feature_id = data.get('featureId')
        feature_name = data.get('featureName')
        feature_description = data.get('featureDescription')

        if not feature_id:
            return https_fn.Response(
                json.dumps({'error': 'Feature ID is required'}),
                status=400,
                headers=cors_headers
            )

        # Get Firestore client
        db = firestore.client()

        # Reference to the feature votes collection
        votes_ref = db.collection('feature_votes')

        # Get the current vote count for this feature
        feature_doc = votes_ref.document(feature_id).get()

        if feature_doc.exists:
            current_votes = feature_doc.to_dict().get('votes', 0)
            # Update the vote count
            votes_ref.document(feature_id).update({
                'votes': current_votes + 1,
                'last_updated': firestore.SERVER_TIMESTAMP
            })
        else:
            # Create new document if it doesn't exist
            votes_ref.document(feature_id).set({
                'votes': 1,
                'feature_name': feature_name or feature_id,
                'feature_description': feature_description or '',
                'last_updated': firestore.SERVER_TIMESTAMP
            })

        print(f"Successfully processed vote for feature: {feature_id}")
        return https_fn.Response(
            json.dumps({
                'success': True,
                'message': 'Vote recorded successfully'
            }),
            status=200,
            headers=cors_headers
        )

    except Exception as e:
        print(f"Error handling feature vote: {str(e)}")
        return https_fn.Response(
            json.dumps({'error': str(e)}),
            status=500,
            headers=cors_headers
        )

@https_fn.on_request()
def handle_unsubscribe(req: https_fn.Request) -> https_fn.Response:
    """Handle unsubscribe requests from email links."""
    print("=== STARTING handle_unsubscribe FUNCTION ===")

    try:
        # Get token from query parameters
        token = req.args.get('token')
        if not token:
            return https_fn.Response(
                json.dumps({"error": "Missing unsubscribe token"}),
                status=400,
                mimetype="application/json"
            )

        print(f"Processing unsubscribe request for token: {token}")

        # Get Firestore client
        db = firestore.client()

        # Find the unsubscribe token
        token_query = db.collection('unsubscribe_tokens').where('token', '==', token).where('used', '==', False).limit(1).get()

        if not token_query:
            return https_fn.Response(
                json.dumps({"error": "Invalid or already used unsubscribe token"}),
                status=400,
                mimetype="application/json"
            )

        token_doc = token_query[0]
        token_data = token_doc.to_dict()
        email = token_data.get('email')

        if not email:
            return https_fn.Response(
                json.dumps({"error": "No email found for unsubscribe token"}),
                status=400,
                mimetype="application/json"
            )

        # Mark token as used
        token_doc.reference.update({
            'used': True,
            'used_at': firestore.SERVER_TIMESTAMP
        })

        # Remove from SendGrid contacts
        sendgrid_api_key = os.environ.get('SENDGRID_API_KEY')
        if sendgrid_api_key:
            try:
                # Delete contact from SendGrid
                delete_response = requests.delete(
                    f"https://api.sendgrid.com/v3/marketing/contacts",
                    headers={
                        "Authorization": f"Bearer {sendgrid_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={"ids": [email]}
                )

                if delete_response.status_code not in [200, 202, 204]:
                    print(f"Warning: Failed to delete contact from SendGrid. Status: {delete_response.status_code}")
                    print(f"Error details: {delete_response.text}")
                else:
                    print(f"Successfully removed contact from SendGrid")
            except Exception as e:
                print(f"Warning: Error removing contact from SendGrid: {str(e)}")

        # Update email signup status in Firestore
        signup_query = db.collection('email_signups').where('email', '==', email).limit(1).get()
        if signup_query:
            signup_doc = signup_query[0]
            signup_doc.reference.update({
                'status': 'unsubscribed',
                'unsubscribed_at': firestore.SERVER_TIMESTAMP
            })

        # Return success response
        return https_fn.Response(
            json.dumps({
                "message": "Successfully unsubscribed from emails",
                "email": email
            }),
            status=200,
            mimetype="application/json"
        )

    except Exception as e:
        print(f"Error in handle_unsubscribe: {str(e)}")
        return https_fn.Response(
            json.dumps({"error": str(e)}),
            status=500,
            mimetype="application/json"
        )

# Update User Timezone
@https_fn.on_call()
def update_timezone(req: https_fn.Request) -> dict:
    try:
        # Get the Firebase UID from the request
        firebase_uid = req.auth.uid
        if not firebase_uid:
            return {"error": "Unauthorized", "status": 401}

        # Get the new timezone from the request
        data = req.data
        if not data or 'timezone' not in data:
            return {"error": "Timezone is required", "status": 400}

        new_timezone = data['timezone']

        # Update the user's timezone in Firestore
        db = firestore.client()
        user_ref = db.collection('users').document(firebase_uid)

        # Update the timezone field
        user_ref.update({
            'timezone': new_timezone,
            'updated_at': firestore.SERVER_TIMESTAMP
        })

        return {"message": "Timezone updated successfully", "status": 200}

    except Exception as e:
        print(f"Error updating timezone: {str(e)}")
        return {"error": "Failed to update timezone", "status": 500}

# -----------------------------
# Firebase Function: delete_strategy
# -----------------------------
@https_fn.on_request()
def delete_strategy(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "DELETE, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }
    if req.method == "OPTIONS":
        return https_fn.Response(json.dumps({"message": "Preflight CORS OK"}),
                                   status=204,
                                   mimetype="application/json",
                                   headers=cors_headers)
    if req.method != "DELETE":
        return https_fn.Response(json.dumps({"message": "Method Not Allowed"}),
                                   status=405,
                                   mimetype="application/json",
                                   headers=cors_headers)
    try:
        data = req.get_json(silent=True)
        if not data or "firebase_uid" not in data or "strategy_id" not in data:
            return https_fn.Response(json.dumps({"message": "Missing required fields"}),
                                   status=400,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Get the strategy document reference
        strategy_ref = db.collection("users").document(data["firebase_uid"]).collection("strategies").document(data["strategy_id"])

        # Check if the strategy exists
        strategy_doc = strategy_ref.get()
        if not strategy_doc.exists:
            return https_fn.Response(json.dumps({"message": "Strategy not found"}),
                                   status=404,
                                   mimetype="application/json",
                                   headers=cors_headers)

        # Delete the strategy
        strategy_ref.delete()

        return https_fn.Response(json.dumps({"message": "Strategy deleted successfully"}),
                               status=200,
                               mimetype="application/json",
                               headers=cors_headers)
    except Exception as e:
        return https_fn.Response(json.dumps({"message": str(e)}),
                               status=500,
                               mimetype="application/json",
                               headers=cors_headers)

# -----------------------------
# Firebase Function: get_forex_pairs_oanda
# -----------------------------
@https_fn.on_request()
def get_forex_pairs_oanda(req: https_fn.Request) -> https_fn.Response:
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight OPTIONS request
    if req.method == "OPTIONS":
        return https_fn.Response(
            json.dumps({"message": "Preflight CORS ok"}),
            status=204,
            mimetype="application/json",
            headers=cors_headers
        )

    if req.method != 'GET':
        return https_fn.Response(
            json.dumps({"message": "Method Not Allowed"}),
            status=405,
            mimetype='application/json',
            headers=cors_headers
        )

    try:
        firebase_uid = req.args.get("firebase_uid")
        if not firebase_uid:
            return https_fn.Response(
                json.dumps({"message": "firebase_uid is required"}),
                status=400,
                mimetype='application/json',
                headers=cors_headers
            )

        # Retrieve the user's document from Firestore
        user_doc = db.collection("users").document(firebase_uid).get()
        if not user_doc.exists:
            return https_fn.Response(
                json.dumps({"message": "User not found"}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )

        user_data = user_doc.to_dict()
        api_key = user_data.get("api_key")
        account_id = user_data.get("account_id")

        if not api_key or not account_id:
            return https_fn.Response(
                json.dumps({"message": "OANDA API key or account ID not found"}),
                status=404,
                mimetype='application/json',
                headers=cors_headers
            )

        # Make request to OANDA API
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        instruments_url = f"{OANDA_API_URL}/accounts/{account_id}/instruments"
        response = requests.get(instruments_url, headers=headers)

        if response.status_code != 200:
            return https_fn.Response(
                json.dumps({"message": "Failed to fetch forex pairs from OANDA"}),
                status=response.status_code,
                mimetype='application/json',
                headers=cors_headers
            )

        data = response.json()
        instruments = data.get("instruments", [])

        # Extract only forex pairs (exclude CFDs, metals, etc.)
        forex_pairs = [instr["name"] for instr in instruments if instr["type"] == "CURRENCY"]

        return https_fn.Response(
            json.dumps({"forex_pairs": forex_pairs}),
            status=200,
            mimetype='application/json',
            headers=cors_headers
        )

    except Exception as e:
        print(f"Error in get_forex_pairs_oanda: {str(e)}")
        return https_fn.Response(
            json.dumps({"message": str(e)}),
            status=500,
            mimetype='application/json',
            headers=cors_headers
        )

# -----------------------------
# Firebase Function: ai_strategy
# -----------------------------
@https_fn.on_request()
def ai_strategy(req: https_fn.Request) -> https_fn.Response:
    """
    Generate a trading strategy using OpenAI based on a user's description.
    The strategy will be in the same format as what users create manually in the strategy-generation.js page.
    """
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Content-Type": "application/json"
    }

    # Handle preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            status=204,
            headers=cors_headers
        )

    try:
        # Get request data
        request_json = req.get_json()
        if not request_json or "message" not in request_json:
            return https_fn.Response(
                json.dumps({"error": "Missing strategy description"}),
                status=400,
                headers=cors_headers
            )

        user_message = request_json["message"]
        print(f"Received strategy description: {user_message}")

        # Initialize OpenAI client
        client = OpenAI(api_key=OPENAI_API_KEY)

        # Create a detailed system prompt that explains the strategy JSON format
        system_prompt = """
You are an expert algorithmic trading strategy designer. Your task is to create a detailed trading strategy in JSON format based on the user's description.

*** PLEASE REMEMBER THAT THE BELOW JSON STRUCTURE IS JUST AN EXAMPLE TO SHOW YOU THE FORMAT, YOU MUST USE YOUR KNOWLEDGE TO CREATE A GREAT STRATEGY THAT PERFORMS WELL WHEN BACKTESTED WHILE REMAINING UNDER THE PERIMETERS PROVIDED BELOW TO YOU IN RULES. ***

Description of your strategy must be very detailed so the user understands what is going on.
The strategy JSON must follow this exact structure to be compatible with the Oryn trading platform:

{
  "name": "Strategy name",
  "description": "Detailed description of the strategy",
  "instruments": "EUR/USD", // Forex pair
  "timeframe": "1h", // Timeframe (1m, 5m, 15m, 30m, 1h, 4h, 1d)
  "tradingSession": ["All"], // Trading sessions: ["All"], ["London"], ["New_York"], ["Tokyo"], ["Sydney"] or combinations
  "indicators": [
    {
      "id": "unique_id_1", // Generate a unique ID using timestamp + random chars
      "indicator_class": "RSI", // Indicator type (RSI, SMA, EMA, MACD, BollingerBands, ATR, SupportResistance)
      "type": "RSI", // Same as indicator_class
      "parameters": { // Parameters specific to each indicator type
        "period": 14 // Example for RSI
      },
      "source": "close" // Data source, One of close/open/high/low/volume/indicator_id
    }
  ],
  "entryRules": [
    {
      "id": "unique_id_2", // Generate a unique ID
      "tradeType": "long", // "long" for buy, "short" for sell
      "indicator1": "price", // Use "price" for price-based rules or indicator ID for indicator-based rules
      "operator": "Crossing above", // Operators: "Crossing above", "Crossing below", ">", "<", "==", ">=", "<="
      "compareType": "value", // "value" or "indicator"
      "indicator2": "", // Only used if compareType is "indicator"
      "value": "30", // Value to compare against if compareType is "value"
      "logicalOperator": "AND", // "AND" or "OR" for combining with next rule
      "barRef": "close" // Reference bar value for price , Must be oneof "close"/"open"/"high"/"low" if "indicator1" is "price" and "close" if "indicator1" is "indicator"
    }
  ],
  "exitRules": [
    {
      "id": "unique_id_3", // Generate a unique ID
      "tradeType": "long", // Should match the entry rule's tradeType
      "indicator1": "price", // Use "price" for price-based rules or indicator ID for indicator-based rules
      "operator": "Crossing below", // Operators: "Crossing above", "Crossing below", ">", "<", "==", ">=", "<="
      "compareType": "value", // "value" or "indicator"
      "indicator2": "", // Only used if compareType is "indicator"
      "value": "70", // Value to compare against if compareType is "value"
      "logicalOperator": "AND", // "AND" or "OR" for combining with next rule
      "barRef": "close" // Reference bar value for price , Must be oneof "close"/"open"/"high"/"low" if "indicator1" is "price" and "close" if "indicator1" is "indicator"
    }
  ],
  "riskManagement": {
    "riskPercentage": "1", // Percentage of account to risk per trade (Must not be empty)
    "riskRewardRatio": "2", // Risk-to-reward ratio (Must not be empty)
    "stopLossMethod": "indicator", // "fixed", "risk", or "indicator"
    "fixedPips": "", // Only Used if stopLossMethod is "fixed" else should be ""
    "indicatorBasedSL": { // Only Used if stopLossMethod is "indicator" else it should be ""
      "indicator": "atr", // "atr", "bollinger", or "support_resistance"
      "parameters": {
        "period": 14,
        "multiplier": 2
      }
    },
    "lotSize": "" // Used if stopLossMethod is "risk" else should be ""
  }
}

IMPORTANT DETAILS FOR EACH INDICATOR TYPE, Only Use Indicators from the below list:

1. SMA (Simple Moving Average):
   - parameters: { "period": 50 }

2. EMA (Exponential Moving Average):
   - parameters: { "period": 20 }

3. RSI (Relative Strength Index):
   - parameters: { "period": 14 }

4. MACD (Moving Average Convergence Divergence):
   - parameters: { "fast": 12, "slow": 26, "signal": 9 }
   - When used in rules, you must add macdComponent and macdComponent2 properties to the rule:
     * For comparing MACD line to signal line: macdComponent: "macd", macdComponent2: "signal"
     * Do NOT use barRef: "macd_line" or barRef: "signal_line" - this is incorrect
     * barRef should be "close" for MACD rules

5. BollingerBands:
   - parameters: { "period": 20, "devfactor": 2, "offset": 0 }
   - When used in rules, you can specify band: "upper", "middle", or "lower"

6. ATR (Average True Range):
   - parameters: { "period": 14 }

7. SupportResistance:
   - parameters: { "left": 10, "right": 10 }

**** YOU MUST RESEPECT THE FOLLOWING RULES AT ALL TIMES AT ALL COSTS ****

RULES FOR GENERATING IDs:
- Each ID must be unique within the strategy
- Format: timestamp (milliseconds) + random string of 8-12 characters
- Example: "1746646401769ml6p3j64kie"

RULES FOR INDICATORS:
- Each indicator must have a unique ID
- If an Indicator is added, it must be used in at least one entry or exit rule
- If you use an indicator for stop loss (in riskManagement.indicatorBasedSL), you MUST also add that same indicator to the indicators array

RULES FOR ENTRY AND EXIT CONDITIONS:
- Entry rules define when to enter a trade (buy/long or sell/short)
- Exit rules define when to exit a trade (must match the trade type of entry)
- For price-based rules, set indicator1 to "price" and barRef to the price type (open, high, low, close)
- For indicator-based rules, set indicator1 to the ID of the indicator
- For comparing indicators to values, use compareType: "value"
- For comparing indicators to other indicators, use compareType: "indicator"
- Valid operators: "Crossing above", "Crossing below", ">", "<", "==", ">=", "<="

PRICE-BASED RULES EXAMPLE:
If the user wants to enter when price crosses above a specific value (e.g., 1.2000):
{
  "id": "1746646401769ml6p3j64kie",
  "tradeType": "long",
  "indicator1": "price",
  "operator": "Crossing above",
  "compareType": "value",
  "indicator2": "",
  "value": "1.2000",
  "logicalOperator": "AND",
  "barRef": "close"
}

MACD RULES EXAMPLE:
If the user wants to enter when MACD line crosses above the signal line:
{
  "id": "1746646401770ml6p3j64kie",
  "tradeType": "long",
  "indicator1": "1746646401769ml6p3j64kie", // ID of the MACD indicator
  "operator": "Crossing above",
  "compareType": "indicator",
  "indicator2": "1746646401769ml6p3j64kie", // Same ID as indicator1
  "value": "",
  "logicalOperator": "AND",
  "barRef": "close",
  "macdComponent": "macd", // IMPORTANT: This specifies we're using the MACD line
  "macdComponent2": "signal" // IMPORTANT: This specifies we're comparing to the signal line
}

RISK MANAGEMENT RULES:
- riskPercentage: Percentage of account to risk per trade (typically "1" to "3")
- riskRewardRatio: Ratio of potential reward to risk (typically "1" to "3")
- stopLossMethod must be only one of:
  ****PLEASE REMEMBER TO ONLY USE ONE OF THE FOLLOWING, OTHER FIELDS RELATED TO OTHER METHODS MUST BE EMPTY***
  1. "fixed": Uses fixedPips for stop loss distance
  2. "risk": Uses lotSize and calculates stop loss based on risk percentage
  3. "indicator": Uses an indicator (ATR, Bollinger Bands, or Support/Resistance) for dynamic stop loss

IMPORTANT: When using indicator-based stop loss, make sure to include that indicator in the indicators array with the same parameters.

IMPORTANT: Generate a complete, valid JSON that follows this exact structure. All IDs must be unique and properly referenced. The strategy should be realistic and executable. Make sure to respect the user's description and include all the elements they mention.
"""

        # Request AI to generate a structured trading strategy
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Create a trading strategy based on this description: {user_message}"}
            ],
            temperature=0.7,
            max_tokens=2000
        )

        # Extract and parse AI response
        raw_response = response.choices[0].message.content.strip()
        print("AI Response:", raw_response)

        # Check if response is empty
        if not raw_response:
            print("Warning: AI returned an empty response.")
            return https_fn.Response(
                json.dumps({"error": "AI response was empty. Try rephrasing your request."}),
                status=500,
                headers=cors_headers
            )

        try:
            # Parse the JSON response
            strategy_data = json.loads(raw_response)

            # Add a description field that includes the original user message
            if "description" not in strategy_data or not strategy_data["description"]:
                strategy_data["description"] = f"Strategy based on: {user_message}"

            # Return the generated strategy
            return https_fn.Response(
                json.dumps(strategy_data),
                status=200,
                headers=cors_headers
            )
        except json.JSONDecodeError as e:
            print(f"Error parsing AI response as JSON: {str(e)}")
            print(f"Raw response: {raw_response}")

            # Try to extract JSON from the response if it's wrapped in markdown code blocks
            import re
            json_match = re.search(r"```(?:json)?\s*([\s\S]+?)\s*```", raw_response)
            if json_match:
                try:
                    strategy_data = json.loads(json_match.group(1))
                    return https_fn.Response(
                        json.dumps(strategy_data),
                        status=200,
                        headers=cors_headers
                    )
                except json.JSONDecodeError:
                    pass

            return https_fn.Response(
                json.dumps({"error": "Failed to parse AI response as valid JSON. Please try again."}),
                status=500,
                headers=cors_headers
            )

    except Exception as e:
        print(f"Error generating strategy: {str(e)}")
        traceback.print_exc()
        return https_fn.Response(
            json.dumps({"error": f"Strategy generation error: {str(e)}"}),
            status=500,
            headers=cors_headers
        )

@https_fn.on_request()
def run_oryn_backtest(req: https_fn.Request) -> https_fn.Response:
    """Run backtest using the Oryn Backtest Engine."""
    # Define CORS headers
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
    }

    # Handle preflight request
    if req.method == "OPTIONS":
        return https_fn.Response(
            status=204,
            headers=cors_headers
        )

    try:
        # Parse request data
        request_data = req.get_json()
        if not request_data or "strategy" not in request_data or "historicalData" not in request_data:
            return https_fn.Response(
                json.dumps({"error": "Missing strategy data or historical data"}),
                status=400,
                headers=cors_headers
            )

        strategy_data = request_data["strategy"]
        historical_data = request_data["historicalData"]
        print(f"Received strategy data: {json.dumps(strategy_data, indent=2)}")

        # Debug: Check if band2 property exists in entry/exit rules
        if "entryRules" in strategy_data:
            for i, rule in enumerate(strategy_data["entryRules"]):
                if "band2" in rule:
                    print(f"Entry rule {i} has band2 property: {rule['band2']}")
                else:
                    print(f"Entry rule {i} does NOT have band2 property")

        if "exitRules" in strategy_data:
            for i, rule in enumerate(strategy_data["exitRules"]):
                if "band2" in rule:
                    print(f"Exit rule {i} has band2 property: {rule['band2']}")
                else:
                    print(f"Exit rule {i} does NOT have band2 property")
        print(f"Received historical data points: {len(historical_data)}")

        try:
            # Convert historical data to DataFrame
            df = pd.DataFrame(historical_data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df = df.sort_index()
            df = df.drop_duplicates()

            # Add timestamp column (required by backtest engine)
            df['timestamp'] = df.index.astype(np.int64) // 10**9  # Convert datetime to Unix timestamp

            print(f"Total data points: {len(df)}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            print(f"Sample timestamps: {df['timestamp'].head().tolist()}")

            # Convert strategy data to our Strategy model
            from oryn_backtest_engine.models.strategy import Strategy, RiskManagement, TradingCosts

            # Prepare risk management data
            risk_management = {}

            # Get risk management data from either 'riskManagement' or 'risk_management' key
            risk_mgmt_data = strategy_data.get('riskManagement', strategy_data.get('risk_management', {}))
            print(f"Raw risk management data from frontend: {risk_mgmt_data}")

            risk_management = {
                # New fields
                "risk_percentage": risk_mgmt_data.get('risk_percentage', '1'),
                "risk_reward_ratio": risk_mgmt_data.get('risk_reward_ratio', '1'),
                "stop_loss_method": risk_mgmt_data.get('stop_loss_method', 'fixed'),
                "fixed_pips": risk_mgmt_data.get('fixed_pips', ''),
                "lot_size": risk_mgmt_data.get('lot_size', '')
            }

            print(f"Using risk management format: {risk_management}")
            if risk_mgmt_data.get('stop_loss_method') == 'indicator' and not risk_mgmt_data.get('indicator_based_sl'):
                raise ValueError("Indicator-based stop loss selected but no indicator data provided")

            if risk_mgmt_data.get('stop_loss_method') == 'fixed' and not risk_mgmt_data.get('fixed_pips'):
                raise ValueError("Fixed pips stop loss selected but no pips value provided")

            if risk_mgmt_data.get('stop_loss_method') == 'risk' and not risk_mgmt_data.get('lot_size'):
                raise ValueError("Risk-based stop loss selected but no lot size value provided")

            # Handle indicator-based stop loss if present
            if risk_mgmt_data.get('stop_loss_method') == 'indicator' and risk_mgmt_data.get('indicator_based_sl'):
                risk_management["indicator_based_sl"] = {
                    "indicator": risk_mgmt_data['indicator_based_sl'].get('indicator', ''),
                    "parameters": risk_mgmt_data['indicator_based_sl'].get('parameters', {})
                }
                risk_management["stop_loss_method"] = 'indicator'

            # Prepare trading costs data
            trading_costs = {
                "spread_pips": float(strategy_data.get('trading_costs', {}).get('spread_pips', 1.0)),
                "commission_percentage": float(strategy_data.get('trading_costs', {}).get('commission_percentage', 0.0)),
                "commission_fixed": float(strategy_data.get('trading_costs', {}).get('commission_fixed', 0.0))
            }

            # Log the risk management data before creating the Strategy instance
            print(f"Risk management data being passed to Strategy.from_dict: {risk_management}")
            # Process entry rules to include band and band2 properties for Bollinger Bands
            entry_rules = []
            for rule in strategy_data["entryRules"] if "entryRules" in strategy_data else strategy_data["entry_rules"]:
                # Debug the rule to see what fields are available
                print(f"Processing entry rule: {json.dumps(rule, indent=2)}")

                # Handle both camelCase and snake_case field names
                trade_type = ""
                if "tradeType" in rule and rule["tradeType"]:
                    trade_type = rule["tradeType"].lower()
                elif "trade_type" in rule and rule["trade_type"]:
                    trade_type = rule["trade_type"].lower()

                compare_type = ""
                if "compareType" in rule and rule["compareType"]:
                    compare_type = rule["compareType"].lower()
                elif "compare_type" in rule and rule["compare_type"]:
                    compare_type = rule["compare_type"].lower()

                # Convert rule to snake_case format
                processed_rule = {
                    "trade_type": trade_type,
                    "indicator1": rule.get("indicator1", ""),
                    "operator": rule.get("operator", ""),
                    "compare_type": compare_type,
                    "indicator2": rule.get("indicator2", None),
                    "value": rule.get("value", None),
                    "logical_operator": rule.get("logicalOperator", rule.get("logical_operator", "AND")),
                    "bar_ref": rule.get("barRef", rule.get("bar_ref", "close")),
                }

                # Add band and band2 properties if they exist
                if "band" in rule:
                    processed_rule["band"] = rule["band"]
                if "band2" in rule:
                    processed_rule["band2"] = rule["band2"]

                entry_rules.append(processed_rule)

            # Process exit rules to include band and band2 properties for Bollinger Bands
            exit_rules = []
            for rule in strategy_data["exitRules"] if "exitRules" in strategy_data else strategy_data["exit_rules"]:
                # Debug the rule to see what fields are available
                print(f"Processing exit rule: {json.dumps(rule, indent=2)}")

                # Handle both camelCase and snake_case field names
                trade_type = ""
                if "tradeType" in rule and rule["tradeType"]:
                    trade_type = rule["tradeType"].lower()
                elif "trade_type" in rule and rule["trade_type"]:
                    trade_type = rule["trade_type"].lower()

                compare_type = ""
                if "compareType" in rule and rule["compareType"]:
                    compare_type = rule["compareType"].lower()
                elif "compare_type" in rule and rule["compare_type"]:
                    compare_type = rule["compare_type"].lower()

                # Convert rule to snake_case format
                processed_rule = {
                    "trade_type": trade_type,
                    "indicator1": rule.get("indicator1", ""),
                    "operator": rule.get("operator", ""),
                    "compare_type": compare_type,
                    "indicator2": rule.get("indicator2", None),
                    "value": rule.get("value", None),
                    "logical_operator": rule.get("logicalOperator", rule.get("logical_operator", "AND")),
                    "bar_ref": rule.get("barRef", rule.get("bar_ref", "close")),
                }

                # Add band and band2 properties if they exist
                if "band" in rule:
                    processed_rule["band"] = rule["band"]
                if "band2" in rule:
                    processed_rule["band2"] = rule["band2"]

                exit_rules.append(processed_rule)

            # Log the processed rules
            print(f"Processed entry rules: {json.dumps(entry_rules, indent=2)}")
            print(f"Processed exit rules: {json.dumps(exit_rules, indent=2)}")

            # Create Strategy instance
            strategy = Strategy.from_dict({
                "indicators": strategy_data["indicators"],
                "entry_rules": entry_rules,
                "exit_rules": exit_rules,
                "risk_management": risk_management,
                "trading_costs": trading_costs,
                "trading_sessions": strategy_data.get("tradingSession", strategy_data.get("trading_sessions", ["All"])),
                "entry_buy_group_operator": strategy_data.get("entryLongGroupOperator", strategy_data.get("entry_buy_group_operator", "AND")),
                "entry_sell_group_operator": strategy_data.get("entryShortGroupOperator", strategy_data.get("entry_sell_group_operator", "AND")),
                "exit_buy_group_operator": strategy_data.get("exitLongGroupOperator", strategy_data.get("exit_buy_group_operator", "OR")),
                "exit_sell_group_operator": strategy_data.get("exitShortGroupOperator", strategy_data.get("exit_sell_group_operator", "OR"))
            })

            # Initialize and run backtest
            from oryn_backtest_engine.engine import OrynBacktestEngine
            initial_capital = 100000.0  # $100k starting capital
            engine = OrynBacktestEngine(df, initial_capital=initial_capital)
            results = engine.run(strategy)

            print(f"Backtest completed with {results['total_trades']} trades")

            # Create a mapping of timestamps to actual dates from our data
            timestamp_to_date = pd.Series(df.index, index=df['timestamp']).to_dict()
            print(f"\nTimestamp mapping sample:")
            sample_timestamps = list(timestamp_to_date.items())[:3]
            for ts, dt in sample_timestamps:
                print(f"Timestamp {ts} -> {dt.isoformat()}")

            # Format trades with correct timestamps from our data
            formatted_trades = []
            for trade in results['trades']:
                # Get the actual datetime for entry and exit
                entry_date = timestamp_to_date.get(trade['entry_time'])
                exit_date = timestamp_to_date.get(trade['exit_time']) if trade.get('exit_time') else None

                if entry_date is None:  # Skip trades with invalid timestamps
                    print(f"⚠️ Warning: Could not find date for trade timestamp {trade['entry_time']}")
                    print(f"Available timestamps: {sorted(list(timestamp_to_date.keys()))[:5]}...")
                    continue

                # Create formatted trade with required fields
                formatted_trade = {
                    'entry_time': entry_date.isoformat(),
                    'entry_price': trade['entry_price'],
                    'type': trade['trade_type'],
                    'size': trade['size'],
                    'entry_rule': trade.get('entry_rule'),
                }

                # Add exit information only if the trade is closed
                if 'exit_time' in trade and 'exit_price' in trade:
                    formatted_trade.update({
                        'exit_time': exit_date.isoformat() if exit_date else None,
                        'exit_price': trade['exit_price'],
                        'gross_pnl': trade.get('gross_pnl', 0),
                        'costs': trade.get('costs', 0),
                        'net_pnl': trade.get('net_pnl', 0),
                        'exit_rule': trade.get('exit_rule'),
                        'exit_reason': trade.get('exit_reason', 'N/A')
                    })
                else:
                    # For open trades, use current price as unrealized exit price
                    current_price = df.iloc[-1]['close']  # Changed from data to df
                    unrealized_pnl = (current_price - trade['entry_price']) * trade['size'] if trade['trade_type'] == 'long' else (trade['entry_price'] - current_price) * trade['size']
                    formatted_trade.update({
                        'exit_time': None,
                        'exit_price': None,
                        'gross_pnl': unrealized_pnl,
                        'costs': 0,  # Costs not realized yet
                        'net_pnl': unrealized_pnl,  # No costs applied yet
                        'exit_rule': None,
                        'exit_reason': 'open'
                    })

                formatted_trades.append(formatted_trade)

            print(f"\nFormatted {len(formatted_trades)} trades")
            if formatted_trades:
                print("Sample trade timestamps:")
                print(f"First trade: Entry={formatted_trades[0]['entry_time']}, Exit={formatted_trades[0].get('exit_time', 'OPEN')}")
                print(f"Last trade: Entry={formatted_trades[-1]['entry_time']}, Exit={formatted_trades[-1].get('exit_time', 'OPEN')}")

            response = {
                "performance": {
                    "initial_value": initial_capital,
                    "final_value": results['final_value'],
                    "gross_pnl": results['gross_profit'] - results['gross_loss'],  # Calculate from components
                    "total_costs": results.get('total_costs', 0),  # Default to 0 if not provided
                    "net_pnl": results['net_pnl'],
                    "total_return": results['total_return'],
                    "roi_percent": results['total_return'] * 100,
                    "total_trades": results['total_trades'],
                    "winning_trades": results['winning_trades'],
                    "losing_trades": results['losing_trades'],
                    "win_rate": results['win_rate'] * 100,
                    "max_drawdown": results['max_drawdown'] * 100,
                    "sharpe_ratio": results['sharpe_ratio'],
                    "average_win": results.get('gross_profit', 0) / results['winning_trades'] if results['winning_trades'] > 0 else 0,
                    "average_loss": results.get('gross_loss', 0) / results['losing_trades'] if results['losing_trades'] > 0 else 0,
                    "profit_factor": results.get('gross_profit', 0) / abs(results.get('gross_loss', 1)) if results.get('gross_loss', 0) != 0 else 0,
                    "best_trade": max((t.get('gross_pnl', 0) for t in formatted_trades), default=0),
                    "worst_trade": min((t.get('gross_pnl', 0) for t in formatted_trades), default=0),
                    "gross_profit": results.get('gross_profit', 0),
                    "gross_loss": results.get('gross_loss', 0),
                    "expectancy": (results.get('gross_profit', 0) / results['winning_trades'] * results['win_rate'] -
                                 results.get('gross_loss', 0) / results['losing_trades'] * (1 - results['win_rate']))
                                if results['total_trades'] > 0 else 0,
                    "avg_trade_duration": results.get('avg_trade_duration', "N/A"),
                    "max_consecutive_wins": results.get('max_consecutive_wins', 0),
                    "max_consecutive_losses": results.get('max_consecutive_losses', 0)
                },
                "trades": formatted_trades,
                "equity_curve": results['equity_curve']
            }

            # Log the response data
            print(f"Sending response with {len(formatted_trades)} trades and equity curve with {len(results['equity_curve'])} points")

            return https_fn.Response(
                json.dumps(response),
                status=200,
                headers=cors_headers
            )

        except Exception as e:
            print(f"Error during backtest: {str(e)}")
            traceback.print_exc()
            return https_fn.Response(
                json.dumps({"error": f"Backtest error: {str(e)}"}),
                status=500,
                headers=cors_headers
            )

    except Exception as e:
        print(f"Error processing request: {str(e)}")
        traceback.print_exc()
        return https_fn.Response(
            json.dumps({"error": str(e)}),
            status=500,
            headers=cors_headers
        )